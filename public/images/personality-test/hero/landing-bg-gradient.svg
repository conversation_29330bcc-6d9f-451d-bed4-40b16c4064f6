<svg width="1440" height="1782" viewBox="0 0 1440 1782" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#paint0_angular_572_1013_clip_path)" data-figma-skip-parse="true"><g transform="matrix(-0.213 0.891 -0.718346 -0.182554 720 891)"><foreignObject x="-1226.06" y="-1226.06" width="2452.11" height="2452.11"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(245, 254, 253, 0.9043) 0deg,rgba(244, 255, 252, 1) 27.8007deg,rgba(243, 255, 253, 0.7) 129.58deg,rgba(246, 252, 255, 0.7) 300.68deg,rgba(245, 254, 253, 0.9043) 360deg);height:100%;width:100%;opacity:1"></div></foreignObject></g></g><rect width="1440" height="1782" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.96000009775161743,&#34;g&#34;:1.0,&#34;b&#34;:0.99040007591247559,&#34;a&#34;:1.0},&#34;position&#34;:0.077224291861057281},{&#34;color&#34;:{&#34;r&#34;:0.95671868324279785,&#34;g&#34;:1.0,&#34;b&#34;:0.99220937490463257,&#34;a&#34;:0.69999998807907104},&#34;position&#34;:0.35994496941566467},{&#34;color&#34;:{&#34;r&#34;:0.96470588445663452,&#34;g&#34;:0.98823529481887817,&#34;b&#34;:1.0,&#34;a&#34;:0.69999998807907104},&#34;position&#34;:0.83522081375122070}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.96000009775161743,&#34;g&#34;:1.0,&#34;b&#34;:0.99040007591247559,&#34;a&#34;:1.0},&#34;position&#34;:0.077224291861057281},{&#34;color&#34;:{&#34;r&#34;:0.95671868324279785,&#34;g&#34;:1.0,&#34;b&#34;:0.99220937490463257,&#34;a&#34;:0.69999998807907104},&#34;position&#34;:0.35994496941566467},{&#34;color&#34;:{&#34;r&#34;:0.96470588445663452,&#34;g&#34;:0.98823529481887817,&#34;b&#34;:1.0,&#34;a&#34;:0.69999998807907104},&#34;position&#34;:0.83522081375122070}],&#34;transform&#34;:{&#34;m00&#34;:-425.99993896484375,&#34;m01&#34;:-1436.6925048828125,&#34;m02&#34;:1651.3460693359375,&#34;m10&#34;:1782.0001220703125,&#34;m11&#34;:-365.10891723632812,&#34;m12&#34;:182.55435180664062},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
<g filter="url(#filter0_f_572_1013)">
<ellipse cx="1025" cy="426" rx="276" ry="223" fill="#48BDA0" fill-opacity="0.46"/>
</g>
<defs>
<clipPath id="paint0_angular_572_1013_clip_path"><rect width="1440" height="1782"/></clipPath><filter id="filter0_f_572_1013" x="429" y="-117" width="1192" height="1086" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="160" result="effect1_foregroundBlur_572_1013"/>
</filter>
</defs>
</svg>
