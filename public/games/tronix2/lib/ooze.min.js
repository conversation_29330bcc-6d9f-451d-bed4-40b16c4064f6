/* Ooze 0.2.6
 * (c) 2019 <PERSON><PERSON><PERSON>
 * MIT License */
"use strict";var ooze={},C_NULL=void 0,C_EMPTYF=function(){},C_VIEW_DEFAULT=0,C_VIEW_CENTER=1,C_VIEW_SCALE_FIT=2,C_VIEW_SCALE_STRETCH=3,C_VIEW_EXPAND=4;ooze.compose=function(){if(Object.assign)return Object.assign.apply(null,arguments);for(var t=arguments[0],e=arguments.length,i=1;i<e;++i){var n=arguments[i];if(n)for(var s in n)n.hasOwnProperty(s)&&(t[s]=n[s])}return t},ooze.clamp=function(t,e,i){return t<=e?e:t>=i?i:t},ooze.getRandomInt=function(t,e){return Math.floor(Math.random()*(e-t+1))+t},ooze.coinFlip=function(){return 1==Math.floor(2*<PERSON>.random())},ooze.shuffle=function(t){for(var e=t.length;--e;){var i=Math.floor(Math.random()*(e+1)),n=t[e];t[e]=t[i],t[i]=n}},ooze.choose=function(t){if(t instanceof Array)return t[Math.floor(Math.random()*t.length)]},ooze.pointInRect=function(t,e,i,n,s,o){return t>=i&&t<i+s&&e>=n&&e<n+o},ooze.pointInCircle=function(t,e,i,n,s){return Math.pow(t-i,2)+Math.pow(e-n,2)<Math.pow(s,2)},ooze.linesIntersect=function(t,e,i,n,s,o,h,a){var r=function(t,e,i,n,s,o){return(o-e)*(i-t)>(n-e)*(s-t)};return r(t,e,s,o,h,a)!=r(i,n,s,o,h,a)&&r(t,e,i,n,s,o)!=r(t,e,i,n,h,a)},ooze.pointToPointDistance=function(t,e,i,n){return Math.sqrt(Math.pow(t-i,2)+Math.pow(e-n,2))},ooze.pointToLineDistance=function(t,e,i,n,s,o){var h=s-i,a=o-n,r=Math.pow(h,2)+Math.pow(a,2);if(0==r)return 0;var f=Math.min(1,Math.max(0,(h*(t-i)+a*(e-n))/r)),c=i+f*h,u=n+f*a;return Math.sqrt(Math.pow(t-c,2)+Math.pow(e-u,2))},ooze.movePoint=function(t,e,i,n,s){var o=180*Math.atan2(n-e,i-t)/Math.PI;return[t+Math.floor(Math.cos(o*Math.PI/180)*s),e+Math.floor(Math.sin(o*Math.PI/180)*s)]},ooze.iter=function(t,e){for(var i in t){var n=t[i];!t.hasOwnProperty(i)||n instanceof Function||e(i,n)}},ooze.getFilenameExtension=function(t){return t.split(".").pop().toLowerCase()},ooze.Grid2D=function(t,e,i){this.width=t,this.height=e,this.data=new Array(t*e),i=void 0===i?0:i,this.clear=function(){for(var n=t*e,s=0;s<n;++s)this.data[s]=i},this.get=function(t,e){return this.data[e*this.width+t]},this.set=function(t,e,i){this.data[e*this.width+t]=i}},ooze.Timer=function(t){(t=t||1)<1&&(t=1);var e=0;this.ticked=!1,this.run=function(){return(e+=1)>=t?(e-=t,this.ticked=!0,!0):(this.ticked=!1,!1)},this.reset=function(){e=0,this.ticked=!1}},ooze.Tween=function(t,e,i,n,s,o,h){if(isNaN(i)||isNaN(e)||isNaN(t)||i<=0)throw new Error("Erroneous parameters for Tween.");o instanceof Function||(o=ooze.ease.linear);var a=null,r=5,f=n instanceof Function,c=s instanceof Function,u=h instanceof Function;function l(d){var y=d/i,k=t+o(y)*(e-t);u&&(k=h(k)),f&&n(k),y<1?a=setTimeout(function(){l(d+r)},r):(f&&n(e),c&&(s(),a=null))}this.stop=function(){clearTimeout(a),a=null},this.start=function(){null===a&&l(0)},this.restart=function(){null!==a&&clearTimeout(a),l(0)},this.finish=function(){null!==a&&(clearTimeout(a),a=null,n instanceof Function&&n(e))},this.isActive=function(){return null!==a}},ooze.ease={linear:function(t){return t},quadIn:function(t){return t*t},quadOut:function(t){return t*(2-t)},quadInOut:function(t){return t<.5?2*t*t:(4-2*t)*t-1},backIn:function(t){return t*t*t-.8*t*Math.sin(t*Math.PI)},backOut:function(t){return t*t*t- -1.2*t*Math.sin(t*Math.PI)}},ooze.AssetLoader=function(){var t=this,e={};this.handler={},this.handler.graphics=function(t,e){var i=new Image;i.src=t,i.addEventListener("load",function(){e(i)})},this.handler.data=function(t,e){var i=new XMLHttpRequest;i.open("get",t,!0),i.send(null),i.onreadystatechange=function(){if(4===i.readyState&&200===i.status){var t=i.responseText,n=JSON.parse(t);e(n)}}},this.handler.text=function(t,e){var i=new XMLHttpRequest;i.open("get",t,!0),i.send(null),i.onreadystatechange=function(){if(4===i.readyState&&200===i.status){var t=i.responseText;e(t)}}},this.load=function(i){var n=i.assets;if(n){var s=i.done||C_EMPTYF,o=i.progress||C_EMPTYF,h=0,a=0;return ooze.iter(n,function(t,e){0,ooze.iter(e,function(){a++})}),ooze.iter(n,function(i,n){var r=t.handler[i];r instanceof Function&&(e[i]||(e[i]={}),ooze.iter(n,function(t,n){r(n,function(n){e[i][t]=n,o(++h,a),h===a&&s()})}))}),e}},this.get=function(){var t,i,n,s,o,h,a,r=arguments.length,f={},c=0;for(h=0;h<r;h++)if("string"==typeof arguments[h])for(t=arguments[h].trim().replace(/,/g,"").split(/\s+/),a=0;a<t.length;a++)2===(i=t[a].split(".")).length&&(n=i[0],s=i[1],o=e[n]?e[n][s]:void 0,f[s]=o,c++);return 1==c?f[Object.keys(f)[0]]:c>1?f:void 0},this.from=function(t){if("string"==typeof t)return new function(t){this.get=function(){var e,i,n,s,o,h=arguments.length,a={},r=0;for(s=0;s<h;s++)if("string"==typeof arguments[s])for(e=arguments[s].trim().replace(/,/g,"").split(/\s+/),o=0;o<e.length;o++)i=e[o],n=t[i],a[i]=n,r++;return 1==r?a[Object.keys(a)[0]]:r>1?a:void 0}}(e[t.trim()])},this.getResources=function(){return e}},ooze.Input=function(t){var e=0,i=1,n=t.getSurface().getCanvas(),s=n.parentElement;function o(e,o){var h,a;switch(t.getViewMode()){case C_VIEW_SCALE_FIT:h=a=n.offsetWidth<s.offsetWidth?n.height/s.offsetHeight:n.width/s.offsetWidth;break;case C_VIEW_SCALE_STRETCH:h=n.width/s.offsetWidth,a=n.height/s.offsetHeight;break;case C_VIEW_EXPAND:case C_VIEW_CENTER:case C_VIEW_DEFAULT:h=a=1}var r=n.getBoundingClientRect(),f=o===i?e.changedTouches[0].clientX:e.clientX,c=o===i?e.changedTouches[0].clientY:e.clientY;return[Math.floor((f-r.left)*h),Math.floor((c-r.top)*a)]}var h={press:[],move:[],release:[]};function a(t,i){if(i.preventDefault&&i.preventDefault(),1===(i.which||i.button)){for(var n=o(i,e),s=t.length;s--;){var h=t[s],a=h[0],r=h[1];r&&!r._active||a(n)}return!1}}function r(t,e){if(e.preventDefault&&e.preventDefault(),!(e.touches.length>1)){for(var n=o(e,i),s=t.length;s--;){var h=t[s],a=h[0],r=h[1];r&&!r._active||a(n)}return!1}}n.addEventListener("mousedown",function(t){a(h.press,t)}),n.addEventListener("touchstart",function(t){r(h.press,t)}),window.addEventListener("mousemove",function(t){!function(t,i){i.preventDefault&&i.preventDefault();for(var n=o(i,e),s=t.length;s--;){var h=t[s],a=h[0],r=h[1];r&&!r._active||a(n)}}(h.move,t)}),window.addEventListener("touchmove",function(t){r(h.move,t)}),window.addEventListener("mouseup",function(t){a(h.release,t)}),window.addEventListener("touchend",function(t){r(h.release,t)}),this.on=function(t,e,i){var n=h[t],s=[e,C_NULL,i];return n.push(s),new function(t){this.bindTo=function(e){e&&(t[1]=e)}}(s)},this.off=function(t,e){if(t||e)if(e){var i=h[t];if(!i)return;for(var n=i.length;n--;){i[n][2]===e&&i.splice(n,1)}}else h[t]=[];else h={press:[],move:[],release:[]}}},ooze.KeyInput=function(){var t=this,e=[],i=[];function n(t,i){e.push({type:t,keycode:i})}this.KEYDOWN=0,this.KEYUP=1,document.addEventListener("keydown",function(e){var s=e.keyCode||e.which;s!==t.keyBackspace&&s!==t.keyAlt||e.preventDefault(),i[s]||(i[s]=!0,n(t.KEYDOWN,s))}),document.addEventListener("keyup",function(e){var s=e.keyCode||e.which;s!==t.keyBackspace&&s!==t.keyAlt||e.preventDefault(),i[s]&&(delete i[s],n(t.KEYUP,s))}),window.onblur=function(){e=[],i=[]},this.clear=function(){e=[],i=[]},this.pollEvent=function(){return e.shift()},this.isAlphanumeric=function(t){return!!(t>47&&t<58||t>64&&t<91||t>96&&t<123)},this.getCharacter=function(t){return String.fromCharCode(t)},this.isKeyDown=function(t){var e=i[t];return void 0!==e&&e},this.keyCancel=3,this.keyHelp=6,this.keyBackspace=8,this.keyTab=9,this.keyClear=12,this.keyEnter=13,this.keyReturn=14,this.keyShift=16,this.keyControl=17,this.keyAlt=18,this.keyPause=19,this.keyCapsLock=20,this.keyEscape=27,this.keySpace=32,this.keyPageUp=33,this.keyPageDown=34,this.keyEnd=35,this.keyHome=36,this.keyLeft=37,this.keyUp=38,this.keyRight=39,this.keyDown=40,this.keyPrintscreen=44,this.keyInsert=45,this.keyDelete=46,this.key0=48,this.key1=49,this.key2=50,this.key3=51,this.key4=52,this.key5=53,this.key6=54,this.key7=55,this.key8=56,this.key9=57,this.keySemicolon=59,this.keyEquals=61,this.keyA=65,this.keyB=66,this.keyC=67,this.keyD=68,this.keyE=69,this.keyF=70,this.keyG=71,this.keyH=72,this.keyI=73,this.keyJ=74,this.keyK=75,this.keyL=76,this.keyM=77,this.keyN=78,this.keyO=79,this.keyP=80,this.keyQ=81,this.keyR=82,this.keyS=83,this.keyT=84,this.keyU=85,this.keyV=86,this.keyW=87,this.keyX=88,this.keyY=89,this.keyZ=90,this.keyContextMenu=93,this.keyNumpad0=96,this.keyNumpad1=97,this.keyNumpad2=98,this.keyNumpad3=99,this.keyNumpad4=100,this.keyNumpad5=101,this.keyNumpad6=102,this.keyNumpad7=103,this.keyNumpad8=104,this.keyNumpad9=105,this.keyMultiply=106,this.keyAdd=107,this.keySeparator=108,this.keySubtract=109,this.keyDecimal=110,this.keyDivide=111,this.keyF1=112,this.keyF2=113,this.keyF3=114,this.keyF4=115,this.keyF5=116,this.keyF6=117,this.keyF7=118,this.keyF8=119,this.keyF9=120,this.keyF10=121,this.keyF11=122,this.keyF12=123,this.keyF13=124,this.keyF14=125,this.keyF15=126,this.keyF16=127,this.keyF17=128,this.keyF18=129,this.keyF19=130,this.keyF20=131,this.keyF21=132,this.keyF22=133,this.keyF23=134,this.keyF24=135,this.keyNumLock=144,this.keyScrollLock=145,this.keyComma=188,this.keyPeriod=190,this.keySlash=191,this.keyBackQuote=192,this.keyOpenBracket=219,this.keyBackSlash=220,this.keyCloseBracket=221,this.keyQuote=222,this.keyMeta=224},ooze.Configuration=function(t,e){var i=this;this.save=function(){localStorage.setItem(t,JSON.stringify(i))},this.load=function(){var n=localStorage.getItem(t),s=n?JSON.parse(n):e;ooze.iter(i,function(t){delete i[t]}),ooze.iter(s,function(t,e){i[t]=e})}},ooze.Render=function(t){this.ctx=t,this.setAlpha=function(e){void 0===e?e=1:e<=0?e=0:e>=1&&(e=1),t.globalAlpha=e},this.rotate=function(e,i){t.save(),t.translate(i[0],i[1]),t.rotate(e*Math.PI/180),t.translate(-i[0],-i[1])},this.restore=function(){t.restore()},this.line=function(e,i,n,s,o,h){o=void 0===o?"#fff":o,h=void 0===h?1:h,t.strokeStyle=o,t.lineWidth=h,t.beginPath(),t.moveTo(e,i),t.lineTo(n,s),t.stroke()},this.rect=function(e,i,n,s,o,h){o=void 0===o?"#fff":o,h=void 0===h?1:h,t.strokeStyle=o,t.lineWidth=h,t.strokeRect(e,i,n,s)},this.rectFill=function(e,i,n,s,o){o=void 0===o?"#fff":o,t.fillStyle=o,t.fillRect(e,i,n,s)},this.arc=function(e,i,n,s,o,h,a){h=void 0===h?"#fff":h,a=void 0===a?1:a,t.strokeStyle=h,t.lineWidth=a,t.arc(e,i,n,s,o),t.stroke()},this.circle=function(e,i,n,s,o){s=void 0===s?"#fff":s,o=void 0===o?1:o,t.strokeStyle=s,t.lineWidth=o,t.arc(e,i,n,0,2*Math.PI),t.stroke()},this.circleFill=function(e,i,n,s){s=void 0===s?"#fff":s,t.fillStyle=s,t.beginPath(),t.arc(e,i,n,0,2*Math.PI),t.fill(),t.closePath()},this.polygon=function(e,i){i=void 0===i?"#fff":i,t.fillStyle=i,t.beginPath(),t.moveTo(e[0][0],e[0][1]);for(var n=e.length,s=1;s<n;s++){var o=e[s];t.lineTo(o[0],o[1])}t.closePath(),t.fill()},this.graphics=function(e,i,n,s,o){void 0===s||void 0===o?t.drawImage(e,i,n):t.drawImage(e,i,n,s,o)},this.surface=function(e,i,n){t.drawImage(e.canvas,i,n)},this.tile=function(e,i,n,s,o,h,a){t.drawImage(e,h,a,s,o,i,n,s,o)},this.stretchTile=function(e,i,n,s,o,h,a,r,f){t.drawImage(e,h,a,s,o,i,n,r,f)},this.text=function(e,i,n,s,o,h){s=void 0===s?"#000":s,o=void 0===o?"left":o,h=void 0===h?"11px sans-serif":h,t.textBaseline="top",t.fillStyle=s,t.textAlign=o,t.font=h,t.fillText(e,i,n)},this.bmptext=function(e,i,n,s,o,h){void 0===o&&(o=0);var a,r,f,c,u=i.length,l=e.width,d=e.height,y=e.gfx,k=e.tilesH*o,v=0,p=e.varwidth,g=0,w=e.tileOffset;if(h>0){if(p){for(r=0;r<u;r++)f=i.charCodeAt(r)-32,v+=e.widths[f]+e.spacing;a=v,v=0}else a=u*l;g=1==h?Math.floor(a/2):a}for(r=0;r<u;r++){var _;if(c=(f=i.charCodeAt(r)-32)-w,p?(_=n+v,v+=e.widths[f]+e.spacing):_=n+r*(l+e.spacing),0!==f){var m=l*(c%e.tilesPerRow),C=d*Math.floor(c/e.tilesPerRow)+k;t.drawImage(y,m,C,l,d,_-g,s,l,d)}}}},ooze.Font=function(t){if(this.gfx=t.graphics,this.width=t.size[0],this.height=t.size[1],this.spacing=void 0===t.spacing?2:t.spacing,this.tileOffset=void 0===t.offset?0:t.offset,this.tilesPerRow=Math.floor(this.gfx.width/this.width),this.tilesH=Math.ceil(95/this.tilesPerRow)*this.height,this.varwidth=void 0!==t.widths,this.varwidth)if(t.widths instanceof Array)this.widths=t.width;else{this.widths=[];for(var e=32;e<=126;e++){var i=String.fromCharCode(e),n=t.widths[i];this.widths.push(n||this.width)}}},ooze.Surface=function(t){var e,i,n;if((t=t||{}).fromCanvas)e=t.fromCanvas,this.width=e.width,this.height=e.height;else{var s=void 0===t.width?100:t.width,o=void 0===t.height?100:t.height;e=document.createElement("canvas"),this.width=e.width=s,this.height=e.height=o}i=e.getContext("2d");var h=this.width,a=this.height;this.canvas=e,this.render=new ooze.Render(i),this.cm_clear=function(){i.clearRect(0,0,this.width,this.height)},this.cm_fill=function(){i.fillStyle=n,i.fillRect(0,0,this.width,this.height)},this.clear=C_NULL,this.setDefaultClearMethod=function(){this.clear=this.cm_clear},this.setFillClearMethod=function(t){n=void 0===t?"#000":t,this.clear=this.cm_fill},this.getCanvas=function(){return e},this.resetSize=function(){this.resize(h,a)},this.resize=function(t,i){this.width=e.width=t,this.height=e.height=i},this.setDefaultClearMethod()},ooze.State=function(t){t=t||{},this._initialized=!1,this._initState=function(t){this._initialized||(this._initialized=!0,this.surface=t,this.paint=t.render,this.init())},this._active=!1,this.surface=C_NULL,this.paint=C_NULL,this.isActive=function(){return this._active},this.init=t.init||C_EMPTYF,this.enter=t.enter||C_EMPTYF,this.exit=t.exit||C_EMPTYF,this.update=t.update||C_EMPTYF,this.draw=t.draw||C_EMPTYF},ooze.Game=function(t){var e,i,n=this,s=C_NULL,o=C_NULL,h=Date.now()/1e3,a=0,r=60,f=1/r;function c(t){var e=Date.now()/1e3,i=e-h;for(i>.25&&(i=.25),h=e,a+=i;a>=f;)s.update(),a-=f;s.draw(),requestAnimationFrame(c)}function u(){s.draw(),requestAnimationFrame(u)}var l=C_VIEW_DEFAULT,d=C_NULL,y=C_NULL,k=20,v={init:{},update:{}};function p(){clearTimeout(y),y=setTimeout(d,k)}v.init[C_VIEW_CENTER]=function(){o.resetSize(),e.style.width=o.width+"px",e.style.height=o.height+"px",e.style.top="0",e.style.left="0",e.style.right="0",e.style.bottom="0",e.style.margin="auto"},v.update[C_VIEW_CENTER]=C_NULL,v.init[C_VIEW_SCALE_FIT]=function(){o.resetSize(),v.update[C_VIEW_SCALE_FIT](),e.style.top="0",e.style.left="0",e.style.right="0",e.style.bottom="0",e.style.margin="auto"},v.update[C_VIEW_SCALE_FIT]=function(){var t,n=o.width,s=o.height,h=i.offsetWidth,a=i.offsetHeight,r=n/s,f=a*r;f>h?(f=h,t=h*(r=s/n)):t=a,e.style.width=f+"px",e.style.height=t+"px"},v.init[C_VIEW_SCALE_STRETCH]=function(){o.resetSize(),e.style.width="100%",e.style.height="100%",e.style.top="0",e.style.left="0"},v.update[C_VIEW_SCALE_STRETCH]=C_NULL,v.init[C_VIEW_EXPAND]=function(){v.update[C_VIEW_EXPAND](),e.style.top="0",e.style.left="0"},v.update[C_VIEW_EXPAND]=function(){o.resize(i.offsetWidth,i.offsetHeight)},this.setState=function(t){s&&(s._active=!1,s.exit()),(s=t)._initialized||s._initState(o),s._active=!0,s.enter()},this.getState=function(){return s},this.getSurface=function(){return o},this.initStates=function(t){for(var e=(t=t instanceof Array?t:[t]).length,i=0;i<e;i++){var n=t[i];n._initialized||n._initState(o)}},this.run=function(){if(t.gameStates instanceof Array)for(var e=t.gameStates.length,i=0;i<e;i++){var s=t.gameStates[i];s._initialized||s._initState(o)}n.setState(t.state),t.simpleLoop?requestAnimationFrame(u):(t.framerate&&(r=t.framerate,f=1/r),requestAnimationFrame(c))},this.setViewMode=function(t){if(d&&(d=C_NULL,window.removeEventListener("resize",p)),"string"==typeof t)switch(t.trim().toLowerCase()){case"scale-fit":case"scale_fit":l=C_VIEW_SCALE_FIT;break;case"scale-stretch":case"scale_stretch":l=C_VIEW_SCALE_STRETCH;break;case"expand":l=C_VIEW_EXPAND;break;default:l=C_VIEW_CENTER}else l=t;e.removeAttribute("style"),e.style.position="absolute",d=v.update[l],v.init[l](),d&&window.addEventListener("resize",p)},this.getViewMode=function(){return l},this.updateView=function(){d&&d()},e=document.getElementById(t.canvasId),i=e.parentElement,o=new ooze.Surface({fromCanvas:e}),t.background&&o.setFillClearMethod(t.background),t.viewMode&&this.setViewMode(t.viewMode)};