/*
 * Aquatica
 *
 * A casual underwater HTML5 game.
 *
 * (c) 2014 <PERSON><PERSON><PERSON>
 *
 */

Aquatica=function(){var e=function(){var e={gfx_fish:"gfx/fish.png",gfx_num:"gfx/num.png",gfx_ui:"gfx/ui.png",gfx_button:"gfx/button.png",gfx_captions:"gfx/captions.png",gfx_anim:"gfx/anim.png",gfx_sky:"gfx/sky.png",gfx_fx:"gfx/fx.png",sfx_score:"sfx/score.ogg",sfx_end:"sfx/end.ogg"};var t={};var r=function(e){return e.split(".").pop().toLowerCase()};var i=function(e,t){for(var n in e)if(e.hasOwnProperty(n))t(n,e[n])};return{Load:function(s,o){var u=n.HasOggSupport();var a=0;var f=0;i(e,function(e,t){var n=r(t);if(["png","jpg","jpeg"].indexOf(n)>-1||u&&"ogg"===n)f++});var l=function(){a++;if(o instanceof Function)o(a,f);if(a==f)s()};i(e,function(e,n){var i=r(n);if(["png","jpg","jpeg"].indexOf(i)>-1){var s=t[e]=new Image;s.src=n;s.addEventListener("load",l,false)}else if(u&&"ogg"===i){var o=t[e]=new Audio;o.src=n;o.addEventListener("canplaythrough",l,false)}})},Get:function(e){return t[e]}}}();var t=function(){var e;return{SetContext:function(t){e=t},SetAlpha:function(t){if(typeof t=="undefined")t=1;if(t>1)t=1;else if(t<0)t=0;e.globalAlpha=t},Clear:function(t,n,r,i){e.clearRect(t,n,r,i)},Text:function(t,n,r,i,s){if(typeof i=="undefined")i="#fff";if(typeof s=="undefined")s="12px sans-serif";e.textBaseline="top";e.fillStyle=i;e.font=s;e.fillText(t,n,r)},Rect:function(t,n,r,i,s,o){if(typeof s=="undefined")s="#fff";if(typeof o=="undefined")o=1;e.strokeStyle=s;e.lineWidth=o;e.strokeRect(t,n,r,i)},RectFill:function(t,n,r,i,s){if(typeof s=="undefined")s="#fff";e.fillStyle=s;e.fillRect(t,n,r,i)},Line:function(t,n,r,i,s,o){if(typeof s=="undefined")s="#fff";if(typeof o=="undefined")o=1;e.strokeStyle=s;e.lineWidth=o;e.moveTo(t,n);e.lineTo(r,i);e.stroke()},Image:function(t,n,r,i,s){if(typeof i!="undefined"&&typeof s!="undefined"){e.drawImage(t,n,r,i,s)}else{e.drawImage(t,n,r)}},Tile:function(t,n,r,i,s,o,u){e.drawImage(t,o,u,i,s,n,r,i,s)},Num:function(t,n,r,i,s){if(typeof s=="undefined")s=0;var o=n.toString();for(var u=0;u<o.length;u++){var a=parseInt(o[u],10);e.drawImage(t,10*a,s*16,10,16,r+11*u,i,10,16)}}}}();var n=function(){var e=false;(function(){var n=document.createElement("audio");e=typeof n.canPlayType==typeof Function&&n.canPlayType("audio/ogg")!==""})();var t=true;return{HasOggSupport:function(){return e},PlaySound:function(n){if(!e||!t)return;n.currentTime=0;n.play()},EnableSounds:function(n){if(!e)return;t=n}}}();var r=function(){var e;var t=0;var n=1;var r=function(t,r){var i=Math.max(e.width/window.innerWidth,e.height/window.innerHeight);var s=e.getBoundingClientRect();var o=r===n?t.changedTouches[0].clientX:t.clientX;var u=r===n?t.changedTouches[0].clientY:t.clientY;return{x:Math.floor((o-s.left)*i),y:Math.floor((u-s.top)*i)}};var i=[];var s=[];var o=[];var u=function(e,n){if(e.preventDefault)e.preventDefault();if(n===t){var s=e.which||e.button;if(s!==1)return}var o=r(e,n);for(var u=0;u<i.length;u++)i[u](o);return false};var a=function(e,t){if(e.preventDefault)e.preventDefault();var n=r(e,t);for(var i=0;i<s.length;i++)s[i](n);return false};var f=function(e,n){if(e.preventDefault)e.preventDefault();if(n===t){var i=e.which||e.button;if(i!==1)return}var s=r(e,n);for(var u=0;u<o.length;u++)o[u](s);return false};return{Init:function(r){e=r;r.addEventListener("mousedown",function(e){u(e,t)},false);r.addEventListener("touchstart",function(e){u(e,n)},false);window.addEventListener("mousemove",function(e){a(e,t)},false);window.addEventListener("touchmove",function(e){a(e,n)},false);window.addEventListener("mouseup",function(e){f(e,t)},false);window.addEventListener("touchend",function(e){f(e,n)},false)},Register:function(e,t){if(e==="press")i.push(t);else if(e==="move")s.push(t);else if(e==="release")o.push(t)}}}();var i=function(e){var t=0;this.Run=function(){t++;if(t>e)t=0};this.Tick=function(){return t==0};this.Reset=function(){t=0}};var s=function(){var s;var o,u,a,f,l,c,h,p;var d,v;var m=false;var g=0;var y=0;var b={bestScore:0,sound:true};var w=function(){localStorage.setItem("aquatica11",JSON.stringify(b))};var E=function(){var e=localStorage.getItem("aquatica11");if(e)b=JSON.parse(e)};var S={FishAnim:new i(8),FishGen:new i(4)};var x=function(e,t,n,r,i,s){return e>=n&&e<=n+i&&t>=r&&t<=r+s};var T=function(e,t){return Math.floor(Math.random()*(t-e+1))+e};var N={visible:true,displayScore:0,DoDrawing:function(){if(!this.visible)return;var e=false;if(this.displayScore==0)return;if(this.displayScore<10){e=true;t.SetAlpha(this.displayScore/10)}t.Tile(a,10,5,52,17,109,0);t.Num(u,this.displayScore,68,5);if(e)t.SetAlpha()},DoLogic:function(){if(this.displayScore<g)this.displayScore++;else if(this.displayScore>g)this.displayScore--}};var C=function(){r.Register("press",function(e){if(x(e.x,e.y,286,14,22,22)){b.sound=!b.sound;w();n.EnableSounds(b.sound)}});return{DoDrawing:function(){var e=b.sound?183:161;t.Tile(a,286,14,22,22,e,0)}}}();var k=function(e){var n=(320-e.width)/2;var i=(480-e.height)/2;this.active=false;this.enabled=false;var s=this;var o=-1;r.Register("press",function(t){if(!s.enabled)return;for(var r=0;r<e.buttons.length;r++){var u=e.buttons[r];if(x(t.x,t.y,n+u.x,i+u.y,140,42)){u.pressed=true;o=r;break}}});r.Register("move",function(t){if(!s.enabled)return;if(o>-1&&o<e.buttons.length){var r=e.buttons[o];r.pressed=x(t.x,t.y,n+r.x,i+r.y,140,42)}});r.Register("release",function(t){if(!s.enabled)return;if(o>-1&&o<e.buttons.length){var n=e.buttons[o];o=-1;if(n.pressed){if(n.OnPush instanceof Function)n.OnPush();n.pressed=false}}});var u=-1;var a=0;var c=1;var h=u;var p=0;var d;this.Show=function(e){d=e;h=a;p=0;this.active=true};this.Hide=function(e){d=e;h=c;this.enabled=false};this.DoDrawing=function(){if(!this.active)return;t.SetAlpha(p/100);t.RectFill(n,i,e.width,e.height,"#fff");t.Rect(n+4.5,i+4.5,e.width-9,e.height-9,"#b8d9e1");t.Rect(n+5.5,i+5.5,e.width-11,e.height-11,"#6584b3");t.SetAlpha();if(!this.enabled){t.SetAlpha(p/100*1.5)}e.DoDrawing(n,i);for(var r=0;r<e.buttons.length;r++){var s=e.buttons[r];t.Tile(f,n+s.x,i+s.y,140,42,0,s.pressed===true?42:0);if(s.hasOwnProperty("caption")){var o=s.caption;var u=70-Math.floor(o.width/2);var a=21-Math.floor(o.height/2);if(s.pressed){u++;a++}t.Tile(l,n+s.x+u,i+s.y+a,o.width,o.height,o.tileX,o.tileY)}}if(!this.enabled)t.SetAlpha()};this.DoLogic=function(){if(!this.active)return;if(h==a){p+=2;if(p>70){h=u;this.enabled=true;if(d instanceof Function){d();d=null}}}else if(h==c){p-=2;if(p<=0){h=u;this.active=false;if(d instanceof Function){d();d=null}}}}};var L={Menu:new k({width:210,height:260,buttons:[{x:40,y:80,caption:{tileX:0,tileY:0,width:46,height:19},OnPush:function(){L.Menu.Hide(function(){m=true;if(g>10)N.displayScore=10;g=0;y=0})}},{x:40,y:130,caption:{tileX:0,tileY:19,width:109,height:19},OnPush:function(){L.Menu.Hide(function(){L.Instructions.Show()})}}],DoDrawing:function(e,n){t.Tile(a,e+54,n+24,109,31,0,0);t.Tile(a,e+7,n+193,196,60,0,31);var r=b.bestScore.toString().length*11;var i=Math.floor((this.width-r)/2);t.Num(u,b.bestScore,e+i,n+226)}}),Instructions:new k({width:260,height:320,buttons:[{x:60,y:260,caption:{tileX:0,tileY:38,width:50,height:19},OnPush:function(){L.Instructions.Hide(function(){L.Menu.Show()})}}],DoDrawing:function(e,n){t.Tile(a,e+10,n+10,238,240,0,91)}})};var A=function(e){var n=new i(e.delay);var r=e.seq||0;this.DoDrawing=function(){var n=e.tiles[r];t.Tile(c,e.x,e.y,e.width,e.height,n[0]*40,n[1]*40)};this.DoLogic=function(){n.Run();if(n.Tick()){r++;if(r>=e.tiles.length)r=0}}};var O=[new A({delay:11,x:60,y:410,width:40,height:80,tiles:[[0,1],[1,1],[2,1]]}),new A({delay:11,x:220,y:434,width:40,height:80,seq:2,tiles:[[0,1],[1,1],[2,1]]}),new A({delay:11,x:290,y:402,width:40,height:80,tiles:[[3,1],[4,1],[5,1]]}),new A({delay:11,x:10,y:432,width:40,height:80,seq:1,tiles:[[3,1],[4,1],[5,1]]})];for(var M=0;M<4;M++){O.push(new A({delay:10,x:M*80,y:41,width:80,height:40,tiles:[[0,0],[2,0],[4,0],[2,0]]}))}var _=function(){var e=T(0,320);return{DoDrawing:function(){t.Tile(h,0,0,e,40,320-e,0);t.Tile(h,e,0,320-e,40,0,0)},DoLogic:function(){e-=.05;if(e<=0)e=320}}}();var D=function(){var e=[];var n=new i(40);var r=function(){var e={x:T(10,300),y:T(390,460),type:T(0,2),seq:0};e.tiles=[[0,e.type],[1,e.type],[2,e.type]];return e};return{DoDrawing:function(){e.forEach(function(e){t.Tile(p,e.x,e.y,40,40,e.tiles[e.seq][0]*40,e.tiles[e.seq][1]*40)})},DoLogic:function(){var t=e.length;while(t--){var i=e[t];i.y-=.8;if(i.y<34)e.splice(t,1);else if(i.y<40)i.seq=2;else if(i.y<46)i.seq=1}if(n.Tick()&&T(0,1)==0){e.push(r())}n.Run()}}}();var P=function(e,n,r){var s=new i(r.delay);var o=0;this.DoDrawing=function(){var i=r.tiles[o];t.Tile(p,e,n,r.width,r.height,i[0]*40,i[1]*40)};this.DoLogic=function(){s.Run();if(s.Tick()){o++;if(o>=r.tiles.length)return true}return false}};var H={Score:{width:80,height:80,tiles:[[0,3],[2,3],[0,5],[2,5]],delay:6},EndGame:{width:80,height:80,tiles:[[0,7],[2,7],[0,9],[2,9]],delay:9}};var B=[];var j=0;var F=1;var I=function(e,n){var r=e.x;var i=e.y;var s=T(0,2);var u=0;var a=e.direction;this.CheckCollision=function(t){return x(t.x,t.y,r+e.bbox[0],i+e.bbox[1],e.bbox[2],e.bbox[3])};this.GetCenter=function(){return{x:r+Math.floor(e.w/2),y:i+Math.floor(e.h/2)}};this.IsGood=function(){return e.good};this.GetScore=function(){return e.score};this.DoDrawing=function(){var n=s;if(a===j)n+=3;var u=e.tiles[n];t.Tile(o,r,i,e.w-1,e.h-1,u[0]*40,u[1]*40)};this.DoLogic=function(){if(a===j){r-=n;if(r<-e.w)return true}else{r+=n;if(r>320)return true}if(S.FishAnim.Tick()){s++;if(s>2)s=0}return false}};var q=function(){var e=[{w:40,h:40,tiles:[[0,0],[1,0],[2,0],[0,1],[1,1],[2,1]],speed:1,bbox:[4,5,32,28],score:20},{w:40,h:40,tiles:[[3,0],[4,0],[5,0],[3,1],[4,1],[5,1]],speed:.8,bbox:[4,4,32,32],score:40},{w:40,h:40,tiles:[[6,0],[7,0],[8,0],[6,1],[7,1],[8,1]],speed:1.2,bbox:[0,8,40,24],score:80}];var t=[{w:40,h:40,tiles:[[0,2],[1,2],[2,2],[0,3],[1,3],[2,3]],speed:.6,bbox:[0,10,40,18]},{w:80,h:40,tiles:[[3,2],[3,3],[3,4],[5,2],[5,3],[5,4]],speed:1.2,bbox:[8,10,64,18]},{w:40,h:40,tiles:[[0,4],[1,4],[2,4],[0,5],[1,5],[2,5]],speed:.3,bbox:[0,10,40,18]}];var n=[{weight:4},{weight:3},{weight:1}];var r=0;for(var i=0;i<n.length;i++){var s=n[i];r+=s.weight;s.csum=r}var o=function(){var e=T(0,r-1);for(var t=0;t<n.length;t++){var i=n[t];if(i.csum>e)return t}return 0};var u=function(e){e.y=T(60,440);e.direction=T(0,1)==1?j:F;if(e.direction==j)e.x=320+e.w;else e.x=-e.w;return e};return{CreateGoodFish:function(){var t=u(e[o()]);t.good=true;return new I(t,t.speed+y)},CreateBadFish:function(){var e=u(t[o()]);e.good=false;return new I(e,e.speed+y)}}}();var R=[];var U=false;var z=function(e){var t=R.length;while(t--){var r=R[t];if(r.CheckCollision(e)){var i=r.GetCenter();if(r.IsGood()){B.push(new P(i.x-40,i.y-40,H.Score));g+=r.GetScore();n.PlaySound(d)}else{m=false;U=false;B.push(new P(i.x-40,i.y-40,H.EndGame));n.PlaySound(v);if(g>b.bestScore){b.bestScore=g;w()}L.Menu.Show();y=0}R.splice(t,1)}}};r.Register("press",function(e){if(!m)return;U=true;z(e)});r.Register("move",function(e){if(!m)return;if(U){z(e)}});r.Register("release",function(e){if(!m)return;U=false});return{Init:function(t){s=t;E();if(n.HasOggSupport()){n.EnableSounds(b.sound)}o=e.Get("gfx_fish");u=e.Get("gfx_num");a=e.Get("gfx_ui");f=e.Get("gfx_button");l=e.Get("gfx_captions");c=e.Get("gfx_anim");h=e.Get("gfx_sky");p=e.Get("gfx_fx");d=e.Get("sfx_score");v=e.Get("sfx_end");for(var r=0;r<500;r++)this.DoLogic();L.Menu.Show()},DoDrawing:function(){O.forEach(function(e){e.DoDrawing()});D.DoDrawing();_.DoDrawing();R.forEach(function(e){e.DoDrawing()});B.forEach(function(e){e.DoDrawing()});N.DoDrawing();if(n.HasOggSupport())C.DoDrawing();for(var e in L){L[e].DoDrawing()}},DoLogic:function(){if(S.FishGen.Tick()){if(T(1,3)==1&&R.length<20){var e;if(T(0,1)==0){e=q.CreateGoodFish()}else{e=q.CreateBadFish()}R.push(e)}}var t=R.length;while(t--){var e=R[t];if(e.DoLogic()){if(m&&g>0&&e.IsGood())g=Math.max(0,g-10);R.splice(t,1)}}O.forEach(function(e){e.DoLogic()});_.DoLogic();var n=B.length;while(n--){if(B[n].DoLogic()){B.splice(n,1)}}D.DoLogic();if(m&&y<2.4){y+=5e-4}for(var r in S){S[r].Run()}N.DoLogic();for(var i in L){L[i].DoLogic()}}}}();var o=function(){var n,i;var o;var u=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)};var a=function(){var e=n.width;var t=n.height;var r=window.innerHeight;if(r!==o){o=r;var f=e/t;var l=r*f;n.style.width=l+"px";n.style.height=r+"px"}i.clearRect(0,0,e,t);s.DoDrawing();s.DoLogic();u(a)};return{Start:function(){n=document.getElementById("aquatica");var o=document.getElementById("aquatica-loadscreen");var u=document.getElementById("aquatica-progressbar");var f=document.getElementById("aquatica-progresslabel");t.SetContext(i=n.getContext("2d"));r.Init(n);e.Load(function(){o.style.display="none";n.style.display="block";s.Init(n);a()},function(e,t){var n=Math.min(100,Math.floor((e+1)*100/t))+"%";f.innerHTML=u.style.width=n})}}}();window.addEventListener("load",o.Start,false)}()
