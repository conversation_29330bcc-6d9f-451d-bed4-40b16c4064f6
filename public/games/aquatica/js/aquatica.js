/*
 * Aquatica
 *
 * A casual underwater HTML5 game.
 *
 * (c) 2014 <PERSON><PERSON><PERSON>
 *
 */

Aquatica = function() {
	// assets module
	var Assets = function() {
		// collection of media assets
		var assets = {
			// graphics
			gfx_fish: '/games/aquatica/gfx/fish.png',
			gfx_num: '/games/aquatica/gfx/num.png',
			gfx_ui: '/games/aquatica/gfx/ui.png',
			gfx_button: '/games/aquatica/gfx/button.png',
			gfx_captions: '/games/aquatica/gfx/captions.png',
			gfx_anim: '/games/aquatica/gfx/anim.png',
			gfx_sky: '/games/aquatica/gfx/sky.png',
			gfx_fx: '/games/aquatica/gfx/fx.png',
			// sound effects
			sfx_score: '/games/aquatica/sfx/score.ogg',
			sfx_end: '/games/aquatica/sfx/end.ogg'
		};

		// compiled collection of assets
		var loadedAssets = {};

		// retreive a filename's extension
		var GetExt = function(filename) {
			return filename.split('.').pop().toLowerCase();
		};

		// iterator
		var Iterate = function(collection, callback) {
			for (var obj in collection)
				if (collection.hasOwnProperty(obj))
					callback(obj, collection[obj]);
		};

		return {
			// load function
			// usage: Load(finished callback, [progress callback])
			Load: function(finished, progress) {
				// check for ogg support
				//var loadOgg = Music.HasOggSupport();
				var loadOgg = Sound.HasOggSupport();

				// track item loading
				var itemsLoaded = 0;

				// count items to load
				var itemsToLoad = 0;
				Iterate(assets, function(key, property) {
					var ext = GetExt(property);
					if (['png', 'jpg', 'jpeg'].indexOf(ext) > -1 ||
						(loadOgg && 'ogg' === ext))
						itemsToLoad++;
				});

				// advance the progress when individual resource is loaded
				var Advance = function() {
					// increment counter
					itemsLoaded++;
					// track progress and finish when all items load
					if (progress instanceof Function)
						progress(itemsLoaded, itemsToLoad);

					if (itemsLoaded == itemsToLoad)
						finished();
				};

				// load media assets
				Iterate(assets, function(key, property) {
					var ext = GetExt(property);
					if (['png', 'jpg', 'jpeg'].indexOf(ext) > -1) {
						// load image asset
						var loadImg = loadedAssets[key] = new Image();
						loadImg.src = property;
						loadImg.addEventListener('load', Advance, false);
					}
					else if (loadOgg && 'ogg' === ext) {
						// load audio asset
						var loadAudio = loadedAssets[key] = new Audio();
						loadAudio.src = property;
						loadAudio.addEventListener('canplaythrough', Advance, false);
					}
				});
			},
			// retreive a media asset
			Get: function(asset) {
				return loadedAssets[asset];
			}
		};
	}();

	// draw module
	var Draw = function() {
		// drawing context
		var ctx;

		return {
			// set drawing context
			SetContext: function(context) {
				ctx = context;
			},
			// sets global alpha
			// usage: SetAlpha ([alpha = 1.0])
			SetAlpha: function(alpha) {
				if (typeof alpha == 'undefined')
					alpha = 1.0;
				if (alpha > 1.0)
					alpha = 1.0;
				else if (alpha < 0.0)
					alpha = 0.0;
				ctx.globalAlpha = alpha;
			},
			// clear area
			// usage: Clear(x, y, width, height)
			Clear: function(x, y, w, h) {
				ctx.clearRect(x, y, w, h);
			},
			// draws text
			// usage: Text(text, x, y, [fillStyle], [font])
			Text: function(text, x, y, fillStyle, font) {
				if (typeof fillStyle == 'undefined')
					fillStyle = '#fff';
				if (typeof font == 'undefined')
					font = '12px sans-serif';
				ctx.textBaseline = 'top';
				ctx.fillStyle = fillStyle;
				ctx.font = font;
				ctx.fillText(text, x, y);
			},
			// draws a rectangle
			// usage: Rect(x, y, width, height, [fillStyle], [lineWidth])
			Rect: function(x, y, w, h, fillStyle, lineWidth) {
				if (typeof fillStyle == 'undefined')
					fillStyle = '#fff';
				if (typeof lineWidth == 'undefined')
					lineWidth = 1;
				ctx.strokeStyle = fillStyle;
				ctx.lineWidth = lineWidth;
				ctx.strokeRect(x, y, w, h);
			},
			// draws a filled rectangle
			// usage: RectFill(x, y, width, height, [fillStyle])
			RectFill: function(x, y, w, h, fillStyle) {
				if (typeof fillStyle == 'undefined')
					fillStyle = '#fff';
				ctx.fillStyle = fillStyle;
				ctx.fillRect(x, y, w, h);
			},
			// draws a line
			// usage: Line(x1, y1, x2, y2, [strokeStyle], [lineWidth])
			Line: function(x1, y1, x2, y2, strokeStyle, lineWidth) {
				if (typeof strokeStyle == 'undefined')
					strokeStyle = '#fff';
				if (typeof lineWidth == 'undefined')
					lineWidth = 1;
				ctx.strokeStyle = strokeStyle;
				ctx.lineWidth = lineWidth;
				ctx.moveTo(x1, y1);
				ctx.lineTo(x2, y2);
				ctx.stroke();
			},
			// draws an image
			// usage: Image(image, x, y, [width], [height])
			Image: function(image, x, y, w, h) {
				if (typeof w != 'undefined' && typeof h != 'undefined') {
					ctx.drawImage(image, x, y, w, h);
				}
				else {
					ctx.drawImage(image, x, y);
				}
			},
			// draws a tile
			// usage: Image(image, x, y, width, height, sx, sy)
			Tile: function(image, x, y, w, h, sx, sy) {
				ctx.drawImage(image, sx, sy, w, h, x, y, w, h);
			},
			// draws numeric glyphs
			// usage: Num(glyph_tiles, number, x, y, [color index])
			Num: function(glyphs, number, x, y, ci) {
				if (typeof ci == 'undefined')
					ci = 0;
				var numstr = number.toString();
				for (var i = 0; i < numstr.length; i++) {
					var n = parseInt(numstr[i], 10);
					ctx.drawImage(glyphs, 10 * n, ci * 16, 10, 16, x + 11 * i, y, 10, 16);
				}
			}
		};
	}();

	// sound module
	var Sound = function() {
		// ogg support flag
		var supportsOgg = false;

		// test for ogg support
		(function TestOgg() {
			var audio = document.createElement('audio');
			supportsOgg = (typeof audio.canPlayType == typeof Function &&
				audio.canPlayType('audio/ogg') !== '');
		})();

		// sound effects enabled flag
		var enableSfx = true;

		return {
			// returns if browser supports ogg playback
			HasOggSupport: function() {
				return supportsOgg;
			},
			// plays a sound
			PlaySound: function(sound) {
				if (!supportsOgg || !enableSfx) return;
				sound.currentTime = 0;
				sound.play();
			},
			// enable / disable sounds
			EnableSounds: function(enable) {
				if (!supportsOgg) return;
				enableSfx = enable;
			}
		};
	}();

	// input module
	var Input = function() {
		// reference to element capturing the input
		var element;

		// input agent constants
		var MOUSE = 0;
		var TOUCH = 1;

		// translates event coordinates to game coordinates
		var TranslateCoords = function(e, agent) {
			var ratio = Math.max(element.width / window.innerWidth, element.height / window.innerHeight);
			var bounds = element.getBoundingClientRect();

			var px = (agent === TOUCH) ? e.changedTouches[0].clientX : e.clientX;
			var py = (agent === TOUCH) ? e.changedTouches[0].clientY : e.clientY;

			return {
				x: Math.floor((px - bounds.left) * ratio),
				y: Math.floor((py - bounds.top) * ratio)
			};
		};

		// event callback lists
		var cbPress = [];
		var cbMove = [];
		var cbRelease = [];

		// event handlers
		var PressEvent = function(e, agent) {
			if (e.preventDefault) e.preventDefault();
			//if (e.stopPropagation) e.stopPropagation();
			if (agent === MOUSE) {
				var button = e.which || e.button;
				if (button !== 1)
					return;
			}
			var coords = TranslateCoords(e, agent);
			for (var i = 0; i < cbPress.length; i++)
				cbPress[i](coords);
			return false;
		};

		var MoveEvent = function(e, agent) {
			if (e.preventDefault) e.preventDefault();
			var coords = TranslateCoords(e, agent);
			for (var i = 0; i < cbMove.length; i++)
				cbMove[i](coords);
			return false;
		};

		var ReleaseEvent = function(e, agent) {
			if (e.preventDefault) e.preventDefault();
			if (agent === MOUSE) {
				var button = e.which || e.button;
				if (button !== 1)
					return;
			}
			var coords = TranslateCoords(e, agent);
			for (var i = 0; i < cbRelease.length; i++)
				cbRelease[i](coords);
			return false;
		};

		return {
			// initializes the input module on given element
			Init: function(el) {
				element = el;
				// attach event listeners
				el.addEventListener('mousedown', function(e) { PressEvent(e, MOUSE); }, false);
				el.addEventListener('touchstart', function(e) { PressEvent(e, TOUCH); }, false);
				window.addEventListener('mousemove', function(e) { MoveEvent(e, MOUSE); }, false);
				window.addEventListener('touchmove', function(e) { MoveEvent(e, TOUCH); }, false);
				window.addEventListener('mouseup', function(e) { ReleaseEvent(e, MOUSE); }, false);
				window.addEventListener('touchend', function(e) { ReleaseEvent(e, TOUCH); }, false);
			},
			// register a callback function
			Register: function(eventType, callback) {
				if (eventType === 'press')
					cbPress.push(callback);
				else if (eventType === 'move')
					cbMove.push(callback);
				else if (eventType === 'release')
					cbRelease.push(callback);
			}
		};
	}();

	// counter based timer class
	var Timer = function(interval) {
		// count
		var count = 0;
		// run ticker
		this.Run = function() {
			count++;
			if (count > interval)
				count = 0;
		};
		// tick function
		this.Tick = function() {
			return (count == 0);
		};
		// reset the timer
		this.Reset = function() {
			count = 0;
		};
	};

	// game module
	var Game = function() {
		// reference to game canvas
		var canvas;

		// game graphics
		var gfx_fish, gfx_num, gfx_ui, gfx_button, gfx_captions, gfx_anim, gfx_sky, gfx_fx;

		// sound effects
		var sfx_score, sfx_end;

		// playing flag
		// indicates if game is being played
		var playing = false;

		// current score
		var score = 0;

		// current relative game speed
		var gameSpeed = 0;

		// game configuration
		var config = {
			bestScore: 0,
			sound: true
		};

		// save config to local storage
		var SaveConfig = function() {
			localStorage.setItem('aquatica11', JSON.stringify(config));
		};

		// load config from local storage
		var LoadConfig = function() {
			var cfg = localStorage.getItem('aquatica11');
			if (cfg)
				config = JSON.parse(cfg);
		};

		// timers
		var tmr = {
			FishAnim: new Timer(8), // fish animation
			FishGen: new Timer(4), // fish generator
		};

		// returns if a point is within rectangle
		var PointInRect = function(x, y, rx, ry, rw, rh) {
			return (x >= rx && x <= rx + rw &&
					y >= ry && y <= ry + rh);
		};

		// retreives a random number in range
		var GetRandInt = function(min, max) {
			return Math.floor(Math.random() * (max - min + 1)) + min;
		};

		// heads-up display
		var HUD = {
			// visible flag
			visible: true,
			// display score
			displayScore: 0,
			// drawing function
			DoDrawing: function() {
				if (!this.visible) return;
				// score
				var transparent = false;
				if (this.displayScore == 0)
					return;
				if (this.displayScore < 10) {
					transparent = true;
					Draw.SetAlpha(this.displayScore / 10);
				}
				Draw.Tile(gfx_ui, 10, 5, 52, 17, 109, 0);
				Draw.Num(gfx_num, this.displayScore, 68, 5);
				if (transparent)
					Draw.SetAlpha();
			},
			// logic function
			DoLogic: function() {
				// spin score
				if (this.displayScore < score)
					this.displayScore++;
				else if (this.displayScore > score)
					this.displayScore--;
			}
		};

		// sound effect toggle button
		var SFXButton = function() {
			// register input event
			Input.Register('press', function(coords) {
				if (PointInRect(coords.x, coords.y, 286, 14, 22, 22)) {
					// toggle sound effects
					config.sound = !config.sound;
					// save settings
					SaveConfig();
					// apply changes to sound module
					Sound.EnableSounds(config.sound);
				}
			});

			return {
				DoDrawing: function() {
					var tileX = (config.sound) ? 183 : 161;
					Draw.Tile(gfx_ui, 286, 14, 22, 22, tileX, 0);
				}
			};
		}();

		// popup class
		var Popup = function(pc) {
			// center the popup
			var x = (320 - pc.width) / 2;
			var y = (480 - pc.height) / 2;

			// active flag
			this.active = false;

			// enabled flag
			this.enabled = false;

			// register events for popup buttons
			var self = this;
			var activeButton = -1;
			Input.Register('press', function(coords) {
				if (!self.enabled) return;
				for (var i = 0; i < pc.buttons.length; i++) {
					var button = pc.buttons[i];
					if (PointInRect(coords.x, coords.y, x + button.x, y + button.y, 140, 42)) {
						button.pressed = true;
						activeButton = i;
						break;
					}
				}
			});
			Input.Register('move', function(coords) {
				if (!self.enabled) return;
				if (activeButton > -1 && activeButton < pc.buttons.length) {
					var button = pc.buttons[activeButton];
					button.pressed = (PointInRect(coords.x, coords.y, x + button.x, y + button.y, 140, 42));
				}
			});
			Input.Register('release', function(coords) {
				if (!self.enabled) return;
				if (activeButton > -1 && activeButton < pc.buttons.length) {
					var button = pc.buttons[activeButton];
					activeButton = -1;
					if (button.pressed) {
						if (button.OnPush instanceof Function)
							button.OnPush();
						button.pressed = false;
					}
				}
			});

			// transition constants
			var NONE = -1;
			var SHOWING = 0;
			var FADING = 1;

			// transition flag
			var transition = NONE;

			// transition sequence
			var tseq = 0;

			// transition callback
			var tcallback;

			// show popup with a fade-in animation
			this.Show = function(tcall) {
				tcallback = tcall;
				transition = SHOWING;
				tseq = 0;
				this.active = true;
			};

			// hide popup with a fade-out animation
			this.Hide = function(tcall) {
				tcallback = tcall;
				transition = FADING;
				//tseq = 0;
				this.enabled = false;
			};

			// drawing function
			this.DoDrawing = function() {
				if (!this.active) return;
				// draw the window
				Draw.SetAlpha(tseq / 100);
				Draw.RectFill(x, y, pc.width, pc.height, '#fff');
				Draw.Rect(x + 4.5, y + 4.5, pc.width - 9, pc.height - 9, '#b8d9e1');
				Draw.Rect(x + 5.5, y + 5.5, pc.width - 11, pc.height - 11, '#6584b3');
				Draw.SetAlpha();
				// draw window graphics
				if (!this.enabled) {
					Draw.SetAlpha(tseq / 100 * 1.5);
				}
				pc.DoDrawing(x, y);
				// draw buttons
				for (var i = 0; i < pc.buttons.length; i++) {
					var button = pc.buttons[i];
					Draw.Tile(gfx_button, x + button.x, y + button.y, 140, 42, 0, (button.pressed === true) ? 42 : 0);
					if (button.hasOwnProperty('caption')) {
						var caption = button.caption;
						var cx = 70 - Math.floor(caption.width / 2);
						var cy = 21 - Math.floor(caption.height / 2);
						if (button.pressed) {
							cx++;
							cy++;
						}
						Draw.Tile(gfx_captions, x + button.x + cx, y + button.y + cy, caption.width, caption.height, caption.tileX, caption.tileY);
					}
				}
				if (!this.enabled)
					Draw.SetAlpha();
			};

			// logic function
			this.DoLogic = function() {
				if (!this.active) return;
				// do transitions
				if (transition == SHOWING) {
					tseq+=2;
					if (tseq > 70) {
						// transition complete
						transition = NONE;
						this.enabled = true;
						if (tcallback instanceof Function) {
							tcallback();
							tcallback = null;
						}
					}
				}
				else if (transition == FADING) {
					tseq-=2;
					if (tseq <= 0) {
						// transition complete
						transition = NONE;
						this.active = false;
						if (tcallback instanceof Function) {
							tcallback();
							tcallback = null;
						}
					}
				}
			};
		};

		// popup windows
		var popups = {
			// main menu
			Menu: new Popup({
				width: 210,
				height: 260,
				buttons: [{
					x: 40,
					y: 80,
					caption: {
						tileX: 0, tileY: 0,
						width: 46,
						height: 19
					},
					OnPush: function() {
						popups.Menu.Hide(function() {
							// start playing
							playing = true;
							if (score > 10)
								HUD.displayScore = 10;
							score = 0;
							gameSpeed = 0;
						});
					}
				}, {
					x: 40,
					y: 130,
					caption: {
						tileX: 0, tileY: 19,
						width: 109,
						height: 19
					},
					OnPush: function() {
						popups.Menu.Hide(function() {
							popups.Instructions.Show();
						});
					}
				}],
				DoDrawing: function(px, py) {
					Draw.Tile(gfx_ui, px + 54, py + 24, 109, 31, 0, 0);
					Draw.Tile(gfx_ui, px + 7, py + 193, 196, 60, 0, 31);
					var numW = config.bestScore.toString().length * 11;
					var numX = Math.floor((this.width - numW) / 2);
					Draw.Num(gfx_num, config.bestScore, px + numX, py + 226);
				}
			}),
			Instructions: new Popup({
				width: 260,
				height: 320,
				buttons: [{
					x: 60,
					y: 260,
					caption: {
						tileX: 0, tileY: 38,
						width: 50, height: 19
					},
					OnPush: function() {
						popups.Instructions.Hide(function() {
							popups.Menu.Show();
						});
					}
				}],
				DoDrawing: function(px, py) {
					Draw.Tile(gfx_ui, px + 10, py + 10, 238, 240, 0, 91);
				}
			})
		};

		// static animation class
		var Anim = function(ac) {

			var animTimer = new Timer(ac.delay);
			var animSeq = ac.seq || 0;

			this.DoDrawing = function() {
				var tile = ac.tiles[animSeq];
				Draw.Tile(gfx_anim, ac.x, ac.y, ac.width, ac.height, tile[0] * 40, tile[1] * 40);
			};

			this.DoLogic = function() {
				animTimer.Run();
				if (animTimer.Tick()) {
					animSeq++;
					if (animSeq >= ac.tiles.length)
						animSeq = 0;
				}
			};
		};

		// static animations
		var animList = [
			// seaweed
			new Anim({
				delay: 11,
				x: 60,
				y: 410,
				width: 40,
				height: 80,
				tiles: [[0, 1], [1, 1], [2, 1]]
			}),
			new Anim({
				delay: 11,
				x: 220,
				y: 434,
				width: 40,
				height: 80,
				seq: 2,
				tiles: [[0, 1], [1, 1], [2, 1]]
			}),
			// small seaweed
			new Anim({
				delay: 11,
				x: 290,
				y: 402,
				width: 40,
				height: 80,
				tiles: [[3, 1], [4, 1], [5, 1]]
			}),
			new Anim({
				delay: 11,
				x: 10,
				y: 432,
				width: 40,
				height: 80,
				seq: 1,
				tiles: [[3, 1], [4, 1], [5, 1]]
			})
		];

		// add wave animations
		for (var i = 0; i < 4; i++) {
			animList.push(new Anim({
				delay: 10,
				x: i * 80,
				y: 41,
				width: 80,
				height: 40,
				tiles: [[0, 0], [2, 0], [4, 0], [2, 0]]
			}));
		}

		// infinite-scrolling sky
		var Sky = function() {
			// tile division point
			var z = GetRandInt(0, 320);

			return {
				DoDrawing: function() {
					Draw.Tile(gfx_sky, 0, 0, z, 40, 320 - z, 0);
					Draw.Tile(gfx_sky, z, 0, 320 - z, 40, 0, 0);
				},
				DoLogic: function() {
					z -= 0.05;
					if (z <= 0)
						z = 320;
				}
			};
		}();

		// bubble effects
		var Bubbles = function() {
			// list of active bubbles
			var bubbleList = [];

			// bubble generator timer
			var bubbleTimer = new Timer(40);

			// create a random bubble
			var CreateBubble = function() {
				var bubble = {
					x: GetRandInt(10, 300),
					y: GetRandInt(390, 460),
					type: GetRandInt(0, 2),
					seq: 0,
				};
				bubble.tiles = [[0, bubble.type], [1, bubble.type], [2, bubble.type]];
				return bubble;
			};

			return {
				DoDrawing: function() {
					// draw individual bubbles
					bubbleList.forEach(function(bubble) {
						Draw.Tile(gfx_fx, bubble.x, bubble.y, 40, 40, bubble.tiles[bubble.seq][0] * 40, bubble.tiles[bubble.seq][1] * 40);
					});
				},
				DoLogic: function() {
					// do bubbles logic
					var i = bubbleList.length;
					while (i--) {
						var bubble = bubbleList[i];
						bubble.y -= 0.8;
						if (bubble.y < 34)
							bubbleList.splice(i, 1);
						else if (bubble.y < 40)
							bubble.seq = 2;
						else if (bubble.y < 46)
							bubble.seq = 1;
					}
					// add a bubble every once in a while
					if (bubbleTimer.Tick() && GetRandInt(0,1) == 0) {
						bubbleList.push(CreateBubble());
					}
					// run the bubble timer
					bubbleTimer.Run();
				}
			};
		}();

		// visual effect class
		var Vfx = function(x, y, fc) {
			var vfxTimer = new Timer(fc.delay);
			var animSeq = 0;

			this.DoDrawing = function() {
				var tile = fc.tiles[animSeq];
				Draw.Tile(gfx_fx, x, y, fc.width, fc.height, tile[0] * 40, tile[1] * 40);
			};
			this.DoLogic = function() {
				// returns true if effect is to be destroyed
				// returns false if effect is still in place
				vfxTimer.Run();
				if (vfxTimer.Tick()) {
					animSeq++;
					if (animSeq >= fc.tiles.length)
						return true;
				}
				return false;
			};
		};

		// effects definitions
		var VEffects = {
			Score: {
				width: 80,
				height: 80,
				tiles: [[0, 3], [2, 3], [0, 5], [2, 5]],
				delay: 6
			},
			EndGame: {
				width: 80,
				height: 80,
				tiles: [[0, 7], [2, 7], [0, 9], [2, 9]],
				delay: 9
			}
		};

		// list of active visual effects
		var vfxList = [];

		// fish direction constants
		var LEFT = 0;
		var RIGHT = 1;

		// fish class
		var Fish = function(fc, speed) {
			var x = fc.x;
			var y = fc.y;

			var animSeq = GetRandInt(0, 2);
			var seq = 0;

			var d = fc.direction;
			//var speed = fc.speed;

			this.CheckCollision = function(coords) {
				// return if point is within fish's bounding box
				return (PointInRect(coords.x, coords.y, x + fc.bbox[0], y + fc.bbox[1], fc.bbox[2], fc.bbox[3]));
			};

			this.GetCenter = function() {
				// get center location of this fish relative to game screen
				return {
					x: x + Math.floor(fc.w / 2),
					y: y + Math.floor(fc.h / 2)
				};
			};

			this.IsGood = function() {
				return fc.good;
			};

			this.GetScore = function() {
				return fc.score;
			};

			this.DoDrawing = function() {
				var ti = animSeq;
				if (d === LEFT)
					ti += 3;
				var tile = fc.tiles[ti];
				Draw.Tile(gfx_fish, x, y, fc.w - 1, fc.h - 1, tile[0] * 40, tile[1] * 40);
			};

			this.DoLogic = function() {
				// returns true if fish is to be destroyed
				// returns false if fish is to keep on existing
				if (d === LEFT) {
					x -= speed;
					if (x < -fc.w)
						return true;
				}
				else {
					x += speed;
					if (x > 320)
						return true;
				}

				if (tmr.FishAnim.Tick()) {
					animSeq++;
					if (animSeq > 2)
						animSeq = 0;
				}

				return false;

			};
		};

		// don't laugh at the name
		var FishFactory = function() {
			// fish varieties
			var goodFish = [
				{ // type 0
					w: 40,
					h: 40,
					tiles: [[0, 0], [1, 0], [2, 0], [0, 1], [1, 1], [2, 1]],
					speed: 1,
					bbox: [4, 5, 32, 28],
					score: 20
				},
				{ // type 1
					w: 40,
					h: 40,
					tiles: [[3, 0], [4, 0], [5, 0], [3, 1], [4, 1], [5, 1]],
					speed: 0.8,
					bbox: [4, 4, 32, 32],
					score: 40
				},
				{ // type 2
					w: 40,
					h: 40,
					tiles: [[6, 0], [7, 0], [8, 0], [6, 1], [7, 1], [8, 1]],
					speed: 1.2,
					bbox: [0, 8, 40, 24],
					score: 80
				}
			];

			var badFish = [
				{ // type 0
					w: 40,
					h: 40,
					tiles: [[0, 2], [1, 2], [2, 2], [0, 3], [1, 3], [2, 3]],
					speed: 0.6,
					bbox: [0, 10, 40, 18]
				},
				{ // type 1
					w: 80,
					h: 40,
					tiles: [[3, 2], [3, 3], [3, 4], [5, 2], [5, 3], [5, 4]],
					speed: 1.2,
					bbox: [8, 10, 64, 18]
				},
				{ // type 2
					w: 40,
					h: 40,
					tiles: [[0, 4], [1, 4], [2, 4], [0, 5], [1, 5], [2, 5]],
					speed: 0.3,
					bbox: [0, 10, 40, 18]
				}
			];

			// weights for random selection
			// bigger weight = more chance that this fish will be picked
			var fishWeights = [
				{ // type 0
					weight: 4
				},
				{ // type 1
					weight: 3
				},
				{ // type 2
					weight: 1
				}
			];

			// total sum of weights
			var totalsum = 0;

			// calculate the cumulative sums and retreive the total sum
			for (var i = 0; i < fishWeights.length; i++) {
				var w = fishWeights[i];
				totalsum += w.weight;
				w.csum = totalsum;
			}

			// pick a fish index at random
			var PickFish = function() {
				var n = GetRandInt(0, totalsum - 1);
				for (var i = 0; i < fishWeights.length; i++) {
					var w = fishWeights[i];
					if (w.csum > n)
						return i;
				}
				return 0;
			};

			// setup the fish
			var SetupFish = function(fish) {
				// randomly pick the y position and a direction
				fish.y = GetRandInt(60, 440);
				fish.direction = (GetRandInt(0, 1) == 1) ? LEFT : RIGHT;
				if (fish.direction == LEFT)
					fish.x = 320 + fish.w;
				else
					fish.x = -fish.w;
				// return the finished fish
				return fish;
			};

			return {
				// creates a good fish of specified level and returns it
				CreateGoodFish: function() {
					// create a good fish at random
					var fc = SetupFish(goodFish[PickFish()]);
					fc.good = true;
					return new Fish(fc, fc.speed + gameSpeed);
				},
				// creates a bad fish of specified level and returns it
				CreateBadFish: function() {
					// create a bad fish at random
					var fc = SetupFish(badFish[PickFish()])
					fc.good = false;
					return new Fish(fc, fc.speed + gameSpeed);
				}
			};
		}();

		// list of active fish
		var fishList = [];

		// input capturing flag
		var captureInput = false;

		// capture fish on given coordinates
		var CaptureFish = function(coords) {
			var fi = fishList.length;
			while (fi--) {
				var fish = fishList[fi];
				if (fish.CheckCollision(coords)) {
					var vfxPos = fish.GetCenter();
					if (fish.IsGood()) {
						// add vfx
						vfxList.push(new Vfx(vfxPos.x - 40, vfxPos.y - 40, VEffects.Score));
						// add score
						score += fish.GetScore();
						// play sound effect
						Sound.PlaySound(sfx_score);
					}
					else {
						// endgame
						playing = false;
						captureInput = false;
						// add vfx
						vfxList.push(new Vfx(vfxPos.x - 40, vfxPos.y - 40, VEffects.EndGame));
						// play sound effect
						Sound.PlaySound(sfx_end);
						// see if new hiscore was reached
						if (score > config.bestScore) {
							// new high score
							config.bestScore = score;
							SaveConfig();
						}
						// show menu
						popups.Menu.Show();
						// reset speed
						gameSpeed = 0;
					}
					// destroy this fish
					fishList.splice(fi, 1);
				}
			}
		}

		// register game input
		Input.Register('press', function(coords) {
			if (!playing) return;
			captureInput = true;
			CaptureFish(coords);
		});

		Input.Register('move', function(coords) {
			if (!playing) return;
			if (captureInput) {
				CaptureFish(coords);
			}
		});

		Input.Register('release', function(coords) {
			if (!playing) return;
			captureInput = false;
		});

		return {
			// initialize game
			Init: function(_canvas) {
				canvas = _canvas;
				// get config from local storage
				LoadConfig();
				// enable or disable sound effects
				if (Sound.HasOggSupport()) {
					Sound.EnableSounds(config.sound);
				}
				// get graphical assets
				gfx_fish = Assets.Get('gfx_fish');
				gfx_num = Assets.Get('gfx_num');
				gfx_ui = Assets.Get('gfx_ui');
				gfx_button = Assets.Get('gfx_button');
				gfx_captions = Assets.Get('gfx_captions');
				gfx_anim = Assets.Get('gfx_anim');
				gfx_sky = Assets.Get('gfx_sky');
				gfx_fx = Assets.Get('gfx_fx');
				// get sound effects
				sfx_score = Assets.Get('sfx_score');
				sfx_end = Assets.Get('sfx_end');
				// run the game a for a few hundred iterations before actually
				// starting the game to populate the game randomly with fish
				for (var n = 0; n < 500; n++)
					this.DoLogic();
				// show the main menu at startup
				popups.Menu.Show();
			},
			DoDrawing: function() {
				// draw animations
				animList.forEach(function(anim) {
					anim.DoDrawing();
				});
				// draw bubbles
				Bubbles.DoDrawing();
				// draw the sky
				Sky.DoDrawing();
				// draw fish
				fishList.forEach(function(fish) {
					fish.DoDrawing();
				});
				// draw visual effects
				vfxList.forEach(function(vfx) {
					vfx.DoDrawing();
				});
				// draw the HUD
				HUD.DoDrawing();
				// draw the SFX button
				if (Sound.HasOggSupport())
					SFXButton.DoDrawing();
				// draw popups
				for (var p in popups) {
					popups[p].DoDrawing();
				}
			},
			DoLogic: function() {
				// fish logic
				// generate fish in sequences
				if (tmr.FishGen.Tick()) {
					// 1/3 chance that we will generate a fish in this try
					// check if the number of fish has reached the maximum number
					if (GetRandInt(1,3) == 1 && fishList.length < 20) {
						var fish;
						// decide on generating either a bad or a good fish
						if (GetRandInt(0,1) == 0) {
							// generate a good fish
							fish = FishFactory.CreateGoodFish();
						}
						else {
							// generate bad fish
							fish = FishFactory.CreateBadFish();
						}
						// push into the array of active fish
						fishList.push(fish);
					}
				}
				// do individual fish logic
				var fi = fishList.length;
				while (fi--) {
					var fish = fishList[fi];
					if (fish.DoLogic()) {
						// take away score if the player lost a good fish
						if (playing && score > 0 && fish.IsGood())
							score = Math.max(0, score - 10);
						// destroy this fish
						fishList.splice(fi, 1);
					}
				}
				// animation logic
				animList.forEach(function(anim) {
					anim.DoLogic();
				});
				// sky logic
				Sky.DoLogic();
				// vfx logic
				var vi = vfxList.length;
				while (vi--) {
					if (vfxList[vi].DoLogic()) {
						// destroy this vfx
						vfxList.splice(vi, 1);
					}
				}
				// bubbles logic
				Bubbles.DoLogic();
				// increase game speed
				if (playing && gameSpeed < 2.4) {
					gameSpeed += 0.0005;
				}
				// timers
				for (var t in tmr) {
					tmr[t].Run();
				}
				// HUD logic
				HUD.DoLogic();
				// popup logic
				for (var p in popups) {
					popups[p].DoLogic();
				}
			}
		};
	}();

	// core module
	var Core = function() {
		// reference to canvas and rendering context
		var canvas, ctx;

		// current window height
		var windowHeight;

		// fallbacks for requestAnimFrame
		var RequestAnimFrame = (
			window.requestAnimationFrame       ||
			window.webkitRequestAnimationFrame ||
			window.mozRequestAnimationFrame    ||
			window.oRequestAnimationFrame      ||
			window.msRequestAnimationFrame     ||
			function (callback) {
				window.setTimeout(callback, 1000 / 60);
			}
		);

		// main game loop
		var Run = function() {
			var cw = canvas.width;
			var ch = canvas.height;

			// track window height for changes
			var dh = window.innerHeight;
			if (dh !== windowHeight) {
				// update viewport
				windowHeight = dh;
				var ratio = cw / ch;
				var width = dh * ratio;
				canvas.style.width = width + 'px';
				canvas.style.height = dh + 'px';
			}

			// clear canvas area
			ctx.clearRect(0, 0, cw, ch);

			// draw to context
			Game.DoDrawing();

			// logic
			Game.DoLogic();

			// loopback
			RequestAnimFrame(Run);
		};

		return {
			Start: () => {
				// retreive canvas
				canvas = document.getElementById('aquatica');

				// retreive loadscreen elements
				const loadscreen = document.getElementById('aquatica-loadscreen');
				const progressbar = document.getElementById('aquatica-progressbar');
				const progresslbl = document.getElementById('aquatica-progresslabel');

				// retreive canvas context and initialize the drawing module
				Draw.SetContext(ctx = canvas.getContext('2d'));

				// initialize the input module
				Input.Init(canvas);

				// load game assets
				Assets.Load(function() {
					// remove the loadscreen and bring up game canvas
          if (loadscreen) loadscreen.style.display = 'none'
          if (canvas) canvas.style.display = 'block';

          // initialize game
					Game.Init(canvas);
					// run the main loop
					Run();
				}, (items, nitems) => {
					// track progress
					const progress = Math.min(100, Math.floor((items + 1) * 100 / nitems)) + '%';

          if (progresslbl) progresslbl.innerHTML = progress
          if (progressbar) progressbar.style.width = progress
				});
			}
		};
	}();

	// start the game as the window loads
	window.addEventListener('load', Core.Start, false);
}();
