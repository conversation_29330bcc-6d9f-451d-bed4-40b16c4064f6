'use client';
import { createContext, useReducer, useEffect, ReactNode } from 'react';
import lscache from 'lscache';
import { IAnswer, ID, IQuestion } from '@/types/personality-test';

// Types
interface QuestionsState {
  questions: IQuestion[];
  selectedQuestion: IQuestion | null;
  activeQuestionId: string | null;
  currentQuestionIndex: number;
  answers: Array<Partial<IAnswer>>;
  localAnswers: { [questionId: string]: Partial<IAnswer> };
  answeredQuestionsOrder: { [questionId: string]: number };
  submittedTest: boolean;
  status: {
    isLoading: boolean;
    isSuccess: boolean;
    isError: boolean;
  };
  message: string | null;
  progress: number;
}

type QuestionsAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_SUCCESS'; payload: boolean }
  | { type: 'SET_ERROR'; payload: boolean }
  | { type: 'SET_MESSAGE'; payload: string | null }
  | { type: 'SET_PROGRESS'; payload: number }
  | { type: 'SET_ANSWER'; payload: { index: number; answer: Partial<IAnswer>; questionId?: ID } }
  | { type: 'RESET_ANSWERS' }
  | { type: 'SET_ACTIVE_QUESTION_ID'; payload: string | null }
  | { type: 'SET_QUESTIONS'; payload: IQuestion[] }
  | { type: 'SET_SUBMITTED_TEST'; payload: boolean }
  | { type: 'LOAD_FROM_STORAGE' };

interface QuestionsContextType extends QuestionsState {
  setAnswer: (index: number, answer: Partial<IAnswer>, questionId?: ID) => void;
  resetAnswers: () => void;
  setActiveQuestionId: (id: string | null) => void;
  setQuestions: (questions: IQuestion[]) => void;
  setSubmittedTest: (submitted: boolean) => void;
  setProgress: (progress: number) => void;
  setLoading: (loading: boolean) => void;
  setSuccess: (success: boolean) => void;
  setError: (error: boolean) => void;
  setMessage: (message: string | null) => void;
}

/**
 * Get item from localStorage
 * @param key - The key to get the item from
 * @param defaultValue - The default value to return if the item is not found
 * @returns The item from localStorage
 */
const getStorageItem = (key: string, defaultValue: any = null) => {
  if (typeof window === 'undefined') return defaultValue;
  try {
    const item = lscache.get(key);
    return item ? item : defaultValue;
  } catch {
    return defaultValue;
  }
};

/**
 * Set item in localStorage
 * @param key - The key to set the item in
 * @param value - The value to set in localStorage
 */
const setStorageItem = (key: string, value: any) => {
  if (typeof window === 'undefined') return;
  try {
    lscache.set(key, value);
  } catch (error) {
    console.warn('Failed to save to localStorage:', error);
  }
};

/**
 * Remove item from localStorage
 * @param key - The key to remove the item from
 */
const removeStorageItem = (key: string) => {
  if (typeof window === 'undefined') return;
  try {
    lscache.remove(key);
  } catch (error) {
    console.warn('Failed to remove from localStorage:', error);
  }
};

/**
 * Get initial state
 * @returns The initial state
 */
const getInitialState = (): QuestionsState => ({
  questions: [],
  selectedQuestion: null,
  activeQuestionId: null,
  currentQuestionIndex: 0,
  answers: [],
  localAnswers: {},
  answeredQuestionsOrder: {},
  submittedTest: false,
  status: {
    isLoading: false,
    isSuccess: false,
    isError: false,
  },
  message: null,
  progress: 0,
});

/**
 * Reducer for the questions
 * @param state - The current state
 * @param action - The action to perform
 * @returns The new state
 */
const questionsReducer = (state: QuestionsState, action: QuestionsAction): QuestionsState => {
  switch (action.type) {
    case 'LOAD_FROM_STORAGE': {
      const storedAnswers = getStorageItem('personality-test-answers', []);
      const localStoredAnswers = getStorageItem('personality-test-local-answers', {});
      const answeredQuestionsOrder = getStorageItem('personality-test-answered-questions-order', {});
      const activeQuestionId = getStorageItem('personality-test-active-question-id', null);
      const submittedTest = getStorageItem('personality-test-submitted-test', false);

      return {
        ...state,
        answers: storedAnswers,
        localAnswers: localStoredAnswers,
        answeredQuestionsOrder,
        activeQuestionId,
        submittedTest,
      };
    }

    case 'SET_LOADING':
      return {
        ...state,
        status: { ...state.status, isLoading: action.payload },
      };

    case 'SET_SUCCESS':
      return {
        ...state,
        status: { ...state.status, isSuccess: action.payload },
      };

    case 'SET_ERROR':
      return {
        ...state,
        status: { ...state.status, isError: action.payload },
      };

    case 'SET_MESSAGE':
      return {
        ...state,
        message: action.payload,
      };

    case 'SET_PROGRESS':
      return {
        ...state,
        progress: action.payload,
      };

    case 'SET_ANSWER': {
      const { index, answer, questionId } = action.payload;
      const newAnswers = [...state.answers];
      newAnswers[index] = answer;

      const newLocalAnswers = { ...state.localAnswers };
      const newAnsweredQuestionsOrder = { ...state.answeredQuestionsOrder };

      if (questionId) {
        newLocalAnswers[`${questionId}`] = answer;
        setStorageItem('personality-test-local-answers', newLocalAnswers);

        // If the question hasn't been answered yet, add its order
        if (!newAnsweredQuestionsOrder[`${questionId}`] && newAnsweredQuestionsOrder[`${questionId}`] !== 0) {
          // Find the maximum current order
          const currentOrders = Object.values(newAnsweredQuestionsOrder);
          const maxOrder = currentOrders.length > 0 ? Math.max(...currentOrders) : -1;

          // Assign the next order to this question
          newAnsweredQuestionsOrder[`${questionId}`] = maxOrder + 1;
          setStorageItem('personality-test-answered-questions-order', newAnsweredQuestionsOrder);
        }
      }

      setStorageItem('personality-test-answers', newAnswers);

      return {
        ...state,
        answers: newAnswers,
        localAnswers: newLocalAnswers,
        answeredQuestionsOrder: newAnsweredQuestionsOrder,
      };
    }

    case 'RESET_ANSWERS': {
      removeStorageItem('personality-test-answers');
      removeStorageItem('personality-test-local-answers');
      removeStorageItem('personality-test-answered-questions-order');
      removeStorageItem('personality-test-active-question-id');

      return {
        ...state,
        answers: [],
        localAnswers: {},
        answeredQuestionsOrder: {},
        activeQuestionId: null,
        currentQuestionIndex: 0,
      };
    }

    case 'SET_ACTIVE_QUESTION_ID': {
      const payload = action.payload;
      let newCurrentQuestionIndex = 0;

      if (payload !== null && payload !== '') {
        const index = state.questions.findIndex(q => q.id === payload);
        newCurrentQuestionIndex = index !== -1 ? index : 0;
        setStorageItem('personality-test-active-question-id', payload);
      } else {
        removeStorageItem('personality-test-active-question-id');
      }

      return {
        ...state,
        activeQuestionId: payload,
        currentQuestionIndex: newCurrentQuestionIndex,
      };
    }

    case 'SET_QUESTIONS': {
      // Sort the questions: answered at the beginning, then unanswered
      const sortedQuestions = [...action.payload].sort((a, b) => {
        const aAnswered = !!state.localAnswers[`${a.id}`]?.answerId;
        const bAnswered = !!state.localAnswers[`${b.id}`]?.answerId;

        // If both are answered, sort by response order
        if (aAnswered && bAnswered) {
          const aOrder = state.answeredQuestionsOrder[`${a.id}`] ?? Number.MAX_SAFE_INTEGER;
          const bOrder = state.answeredQuestionsOrder[`${b.id}`] ?? Number.MAX_SAFE_INTEGER;
          return aOrder - bOrder;
        }

        // If only one is answered, put the answered question first
        if (aAnswered && !bAnswered) return -1;
        if (!aAnswered && bAnswered) return 1;
        return 0;
      });

      // Initialize the index to the number of answered questions
      const answeredCount = sortedQuestions.filter(q => !!state.localAnswers[`${q.id}`]?.answerId).length;
      // Avoid out-of-range indices when there are no questions
      const hasQuestions = sortedQuestions.length > 0;
      const lastIndex = hasQuestions ? sortedQuestions.length - 1 : 0;
      const currentQuestionIndex = hasQuestions ? Math.min(answeredCount, lastIndex) : 0;
      const activeQuestionId = sortedQuestions[currentQuestionIndex]?.id || null;

      if (activeQuestionId) {
        setStorageItem('personality-test-active-question-id', activeQuestionId);
      }

      return {
        ...state,
        questions: sortedQuestions,
        currentQuestionIndex,
        activeQuestionId,
      };
    }

    case 'SET_SUBMITTED_TEST': {
      setStorageItem('personality-test-submitted-test', action.payload);
      return {
        ...state,
        submittedTest: action.payload,
      };
    }

    default:
      return state;
  }
};

/**
 * Create Context
 * @param state - The current state
 * @param action - The action to perform
 * @returns The new state
 */
export const QuestionsContext = createContext<QuestionsContextType | undefined>(undefined);

// Provider Component
interface QuestionsProviderProps {
  children: ReactNode;
}

export const QuestionsProvider = ({ children }: QuestionsProviderProps) => {
  const [state, dispatch] = useReducer(questionsReducer, getInitialState());

  // Load data from storage on mount
  useEffect(() => {
    dispatch({ type: 'LOAD_FROM_STORAGE' });
  }, []);

  // Reorganize questions when localAnswers or answeredQuestionsOrder change
  useEffect(() => {
    if (
      state.questions.length > 0 &&
      (Object.keys(state.localAnswers).length > 0 || Object.keys(state.answeredQuestionsOrder).length > 0)
    ) {
      dispatch({ type: 'SET_QUESTIONS', payload: state.questions });
    }
  }, [state.localAnswers, state.answeredQuestionsOrder, state.questions.length]);

  // Context methods
  const setAnswer = (index: number, answer: Partial<IAnswer>, questionId?: ID) => {
    dispatch({ type: 'SET_ANSWER', payload: { index, answer, questionId } });
  };

  const resetAnswers = () => {
    dispatch({ type: 'RESET_ANSWERS' });
  };

  const setActiveQuestionId = (id: string | null) => {
    dispatch({ type: 'SET_ACTIVE_QUESTION_ID', payload: id });
  };

  const setQuestions = (questions: IQuestion[]) => {
    dispatch({ type: 'SET_QUESTIONS', payload: questions });
  };

  const setSubmittedTest = (submitted: boolean) => {
    dispatch({ type: 'SET_SUBMITTED_TEST', payload: submitted });
  };

  const setProgress = (progress: number) => {
    dispatch({ type: 'SET_PROGRESS', payload: progress });
  };

  const setLoading = (loading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: loading });
  };

  const setSuccess = (success: boolean) => {
    dispatch({ type: 'SET_SUCCESS', payload: success });
  };

  const setError = (error: boolean) => {
    dispatch({ type: 'SET_ERROR', payload: error });
  };

  const setMessage = (message: string | null) => {
    dispatch({ type: 'SET_MESSAGE', payload: message });
  };

  const contextValue: QuestionsContextType = {
    ...state,
    setAnswer,
    resetAnswers,
    setActiveQuestionId,
    setQuestions,
    setSubmittedTest,
    setProgress,
    setLoading,
    setSuccess,
    setError,
    setMessage,
  };

  return <QuestionsContext.Provider value={contextValue}>{children}</QuestionsContext.Provider>;
};
