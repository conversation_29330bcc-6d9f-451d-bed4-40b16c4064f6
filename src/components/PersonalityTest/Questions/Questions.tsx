'use client';
import { useTranslations } from 'next-intl';
import { useState, useEffect } from 'react';
import { IAnswer, IQuestion } from '@/types/personality-test';
import { PersonalityTestQuestionsManager } from '@/services/personality-test-questions-manager';
import { usePersonalityQuestions } from '@/hooks/usePersonalityQuestions';
import TestProgressBar from '@/components/ProgressBar/TestProgressBar';
import { defaultIcons } from './DefaultIcons';
import Logo from '../Layout/Logo';

export default function PersonalityTestQuestions({ questions }: { questions: IQuestion[] }) {
  const t = useTranslations('personality_test.questions');
  // Context hooks
  const {
    currentQuestionIndex,
    localAnswers,
    status,
    submittedTest,
    setAnswer,
    resetAnswers,
    setActiveQuestionId,
    setQuestions,
    setSubmittedTest,
  } = usePersonalityQuestions();

  const [selectedOptionId, setSelectedOptionId] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const [organizedQuestions, setOrganizedQuestions] = useState<IQuestion[]>([]);

  // Organize the questions client side at the component mount
  useEffect(() => {
    if (questions && questions.length > 0) {
      // Use QuestionsManager to organize the questions client side
      const sortedQuestions = PersonalityTestQuestionsManager.organizeQuestions(questions);
      setOrganizedQuestions(sortedQuestions);
      setQuestions(sortedQuestions);

      // Calculate the progress
      const initialProgress = PersonalityTestQuestionsManager.calculateProgress(questions);
      setProgress(initialProgress);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Reorganize questions when localAnswers change (e.g., after page reload)
  useEffect(() => {
    if (questions && questions.length > 0 && Object.keys(localAnswers).length > 0) {
      const sortedQuestions = PersonalityTestQuestionsManager.organizeQuestions(questions);
      setOrganizedQuestions(sortedQuestions);
      setQuestions(sortedQuestions);

      // Update progress
      const updatedProgress = PersonalityTestQuestionsManager.calculateProgress(questions);
      setProgress(updatedProgress);

      // After reload, set index to the number of answered questions (or answeredCount - 1 to stay on last answered)
      const answeredCount = sortedQuestions.filter(q => q.id && !!localAnswers[q.id]?.answerId).length;
      if (answeredCount > 0) {
        // Go to the last answered question (answeredCount - 1) so user can continue from there
        const targetIndex = Math.max(0, answeredCount - 1);
        setActiveQuestionId(sortedQuestions[targetIndex]?.id || null);
      }
    }
  }, [questions, localAnswers]);

  useEffect(() => {
    const currentQuestions = organizedQuestions.length > 0 ? organizedQuestions : questions;
    const currentQuestion = currentQuestions[currentQuestionIndex];
    const currentLocalAnswer = currentQuestion ? localAnswers[`${currentQuestion.id}`] : null;
    setSelectedOptionId(currentLocalAnswer?.answerId || null);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentQuestionIndex, localAnswers]);

  const handleAnswerChange = (question: IQuestion, answer: Partial<IAnswer>) => {
    setAnswer(currentQuestionIndex, answer, question?.id!);
    setSelectedOptionId(answer.answerId || null);

    // Save the answer with its order
    if (question.id) {
      PersonalityTestQuestionsManager.saveAnswer(question.id.toString(), answer);

      // Update the progress
      const updatedProgress = PersonalityTestQuestionsManager.calculateProgress(questions);
      setProgress(updatedProgress);
    }

    // Navigate to the next question
    const currentQuestions = organizedQuestions.length > 0 ? organizedQuestions : questions;
    if (currentQuestionIndex < currentQuestions.length - 1) {
      const nextQuestionId = currentQuestions[currentQuestionIndex + 1].id!;
      setActiveQuestionId(nextQuestionId);
    } else {
      // If it's the last question, check if there are unanswered questions
      const unansweredQuestions = currentQuestions.filter(q => q.id && !localAnswers[q.id]?.answerId);
      if (unansweredQuestions.length > 0) {
        // Go to the first unanswered question
        const firstUnansweredQuestionId = unansweredQuestions[0].id;
        if (firstUnansweredQuestionId) {
          setActiveQuestionId(firstUnansweredQuestionId);
        }
      }
    }
  };

  const handleBack = () => {
    if (currentQuestionIndex > 0) {
      const currentQuestions = organizedQuestions.length > 0 ? organizedQuestions : questions;
      const previousQuestionId = currentQuestions[currentQuestionIndex - 1].id!;
      setActiveQuestionId(previousQuestionId);
      const previousAnswer = localAnswers[previousQuestionId];
      setSelectedOptionId(previousAnswer?.answerId || null);
    }
  };

  if (status.isLoading) {
    // TODO: Add a loading skeleton
    return <div>Loading...</div>;
  }

  const handleRestart = () => {
    PersonalityTestQuestionsManager.resetAnswers();
    resetAnswers();
    setSubmittedTest(false);
    setProgress(0);
  };

  const handleSubmit = () => {};

  // Use the organized questions if available
  const currentQuestions = organizedQuestions.length > 0 ? organizedQuestions : questions;
  const currentQuestion = currentQuestions[currentQuestionIndex];
  const unansweredCount = currentQuestions.filter(q => !localAnswers[`${q.id}`]?.answerId).length;

  const questionOptions = [...(currentQuestion?.questionAnswerOptions ?? [])].slice().sort((a, b) => {
    if (a.order === undefined || b.order === undefined) {
      return (b.score ?? 0) - (a.score ?? 0);
    }
    return a.order - b.order;
  });

  return (
    <div className="m-[auto]">
      <header className="w-full h-[60px] md:h-[110px] flex justify-center items-center pt-[4px] md:pt-[12px]">
        <Logo />
      </header>
      <TestProgressBar progress={progress} color="#48BDA0" borderColor="#E4FFF3" />
      <main className="max-w-[343px] md:max-w-[619px] mx-auto mt-[32px] md:mt-[60px]">
        <div className="max-w-[636px] mx-auto md:pt-10 pt-5 flex flex-col h-full">
          <p className="md:text-[18px] text-base px-4 font-semibold md:mb-8 mb-1 md:min-h-max min-h-[72px]">
            {currentQuestion?.title}
          </p>
          <div className="flex flex-col gap-4 xl:h-full overflow-y-auto px-4 pb-4">
            {questionOptions?.map((option, index) => (
              <label
                className={`group opinion cursor-pointer border border-transparent active:bg-[#48BDA01A] active:border-[#48BDA0] text-[#292929] p-4 bg-white rounded-[10px] shadow-cmn-shadow flex items-center justify-between transition-all ease-in-out duration-300 md:hover:border-[#48BDA0] md:hover:shadow-none ${
                  selectedOptionId === option.id ? 'bg-[#48BDA01A] border-[#48BDA0] option-selected' : ''
                }`}
                key={option.id}>
                <input
                  type="radio"
                  name={`question-${currentQuestion?.id}`}
                  value={option.score}
                  className="invisible hidden"
                  onClick={() => {
                    handleAnswerChange(currentQuestion, {
                      questionId: currentQuestion?.id,
                      question: currentQuestion,
                      answerId: option.id,
                      answer: option,
                    });
                  }}
                  checked={selectedOptionId === option.id}
                  readOnly
                />
                <p>{option.title}</p>
                <div className="opinion-emoji flex gap-1 items-center">{defaultIcons[index].image}</div>
              </label>
            ))}

            <div className="flex items-center justify-between md:pt-10 pt-4">
              {currentQuestionIndex > 0 && (
                <button
                  className="bg-white md:py-3.5 py-2 px-2 flex justify-center items-center rounded-[10px] text-center shadow-cmn-shadow font-semibold md:text-lg text-base text-[#48BDA0] md:min-h-[58px] min-h-12 md:min-w-36 min-w-28 transition-all ease-in-out duration-300 hover:bg-[#48BDA0] hover:text-white hover:shadow-none"
                  onClick={handleBack}>
                  {t('back_button')}
                </button>
              )}
              {submittedTest && (
                <button
                  className="ml-auto bg-[#48BDA0] md:py-3.5 py-2 px-2 flex justify-center items-center rounded-[10px] text-center font-semibold md:text-lg text-base text-white md:min-h-[58px] min-h-12 md:min-w-36 min-w-28 transition-all ease-in-out duration-300 hover:bg-white hover:text-[#48BDA0] shadow-cmn-shadow"
                  onClick={handleRestart}>
                  {t('restart_button')}
                </button>
              )}

              {currentQuestionIndex < currentQuestions.length - 1 ? (
                <div></div>
              ) : (
                <button
                  className="ml-auto bg-[#48BDA0] md:py-3.5 py-2 px-2 flex justify-center items-center rounded-[10px] text-center font-semibold md:text-lg text-base text-white md:min-h-[58px] min-h-12 md:min-w-36 min-w-28 transition-all ease-in-out duration-300 hover:bg-white hover:text-[#48BDA0] shadow-cmn-shadow"
                  onClick={handleSubmit}
                  disabled={unansweredCount > 0}>
                  {t('submit_button')}
                </button>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
