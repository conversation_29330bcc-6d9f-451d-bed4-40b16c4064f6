import { useContext } from 'react';
import { QuestionsContext } from '@/store/PersonalityTestContext';

/**
 * Custom hook to use the Personality Test Context
 * @returns The context
 */
export const usePersonalityQuestions = () => {
  const context = useContext(QuestionsContext);
  if (context === undefined) {
    throw new Error('useQuestions must be used within a QuestionsProvider');
  }
  return context;
};
