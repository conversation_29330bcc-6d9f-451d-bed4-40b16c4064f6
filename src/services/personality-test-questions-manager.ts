// import { httpsCallable } from 'firebase/functions';
// import { functions } from '@/utils/firebase';
import lscache from 'lscache';
import { IAnswer, IQuestion } from '@/types/personality-test';
import getPersonalityTestQuestions from '@/constants/personality-test-questions';

// Check if we are on the client (where localStorage is available)
const isClient = typeof window !== 'undefined';

/**
 * <PERSON>le questions with safe methods for server and client
 */
export class PersonalityTestQuestionsManager {
  /**
   * Fetch questions from the API (compatible server and client)
   */
  static async fetchQuestionsFromAPI(): Promise<IQuestion[]> {
    try {
      // Uncomment this when the API is ready to integrate

      // const callable = httpsCallable(functions, 'fetchQuestions');
      // const response: any = await callable();
      // return response.data;

      return getPersonalityTestQuestions();
    } catch (error) {
      console.error('Erreur lors de la récupération des questions:', error);
      return [];
    }
  }

  /**
   * Organize questions with answered questions first (client only)
   * Does nothing on server
   */
  static organizeQuestions(questions: IQuestion[]): IQuestion[] {
    // Server, return questions without modification
    if (!isClient) {
      return questions;
    }

    // Client, organize questions
    try {
      // Get answered questions
      const storedAnswers = lscache.get('personality-test-local-answers');
      const localAnswers = storedAnswers ? storedAnswers : {};

      // Get answered orders
      const ordersData = lscache.get('personality-test-answered-questions-order');
      const answeredOrders = ordersData ? ordersData : {};

      // Sort questions with answered questions first
      return [...questions].sort((a, b) => {
        const aAnswered = !!localAnswers[`${a.id}`];
        const bAnswered = !!localAnswers[`${b.id}`];

        // If both are answered, sort by order
        if (aAnswered && bAnswered) {
          const aOrder = answeredOrders[`${a.id}`] ?? Number.MAX_SAFE_INTEGER;
          const bOrder = answeredOrders[`${b.id}`] ?? Number.MAX_SAFE_INTEGER;
          return aOrder - bOrder;
        }

        // Put answered questions first
        if (aAnswered && !bAnswered) return -1;
        if (!aAnswered && bAnswered) return 1;

        return 0;
      });
    } catch (error) {
      console.error('Error organizing questions:', error);
      return questions;
    }
  }

  /**
   * Calculate progress percentage (client only)
   */
  static calculateProgress(questions: IQuestion[]): number {
    if (!isClient) {
      return 0;
    }

    try {
      const storedAnswers = lscache.get('personality-test-local-answers');
      const localAnswers = storedAnswers ? storedAnswers : {};
      const answeredCount = Object.keys(localAnswers).length;
      return (answeredCount / questions.length) * 100;
    } catch (error) {
      console.error('Error calculating progress:', error);
      return 0;
    }
  }

  /**
   * Save an answer with its order (client only)
   */
  static saveAnswer(questionId: string, answer: Partial<IAnswer>): void {
    if (!isClient) {
      return;
    }

    try {
      // Get existing answers
      const storedAnswers = lscache.get('personality-test-local-answers');
      const localAnswers = storedAnswers ? storedAnswers : {};

      // Get existing orders
      const ordersData = lscache.get('personality-test-answered-questions-order');
      const answeredOrders = ordersData ? ordersData : {};

      // If the question doesn't have an order, assign a new one
      if (answeredOrders[questionId] === undefined) {
        const currentOrders = Object.values(answeredOrders);
        const maxOrder = currentOrders.length > 0 ? Math.max(...(currentOrders as number[])) : -1;
        answeredOrders[questionId] = maxOrder + 1;
      }

      // Save the answer
      localAnswers[questionId] = answer;

      // Save in localStorage
      lscache.set('personality-test-local-answers', localAnswers);
      lscache.set('personality-test-answered-questions-order', answeredOrders);
    } catch (error) {
      console.error('Error saving answer:', error);
    }
  }

  /**
   * Reset all stored answers (client only)
   */
  static resetAnswers(): void {
    if (!isClient) {
      return;
    }

    try {
      lscache.remove('personality-test-answers');
      lscache.remove('personality-test-local-answers');
      lscache.remove('personality-test-answered-questions-order');
      lscache.remove('personality-test-active-question-id');
    } catch (error) {
      console.error('Error resetting answers:', error);
    }
  }
}
